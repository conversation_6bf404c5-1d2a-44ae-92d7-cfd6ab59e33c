{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue", "mtime": 1754384002001}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}