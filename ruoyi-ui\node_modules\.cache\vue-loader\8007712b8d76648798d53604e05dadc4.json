{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue?vue&type=style&index=1&id=7928017a&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue", "mtime": 1754384002001}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZm9ybS1jb25maWctY29udGFpbmVyIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQouY29uZmlnLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogMjBweCAyNHB4Ow0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLmNvbmZpZy1oZWFkZXIgaDMgew0KICBtYXJnaW46IDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXNpemU6IDE4cHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi5jb25maWctY29udGVudCB7DQogIHBhZGRpbmc6IDI0cHg7DQogIG1pbi1oZWlnaHQ6IDIwMHB4Ow0KfQ0KDQouZW5hYmxlZC1jb25maWcgew0KICBib3JkZXI6IDFweCBzb2xpZCAjNjdjMjNhOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIHBhZGRpbmc6IDE2cHg7DQogIGJhY2tncm91bmQ6ICNmMGY5ZmY7DQp9DQoNCi5lbmFibGVkLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTJweDsNCn0NCg0KLmVuYWJsZWQtaGVhZGVyIGg0IHsNCiAgbWFyZ2luOiAwOw0KICBjb2xvcjogIzY3YzIzYTsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCn0NCg0KLmVuYWJsZWQtZGVzY3JpcHRpb24gew0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBjb2xvcjogIzYwNjI2NjsNCn0NCg0KLmZvcm0tcHJldmlldyBoNSB7DQogIG1hcmdpbjogMCAwIDEycHggMDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLmZpZWxkLWxpc3Qgew0KICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5maWVsZC1pdGVtIHsNCiAgcGFkZGluZzogMTJweCAxNnB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCiAgYmFja2dyb3VuZDogI2ZhZmJmYzsNCn0NCg0KLmZpZWxkLWl0ZW06bGFzdC1jaGlsZCB7DQogIGJvcmRlci1ib3R0b206IG5vbmU7DQp9DQoNCi5maWVsZC1pbmZvIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxMnB4Ow0KfQ0KDQouZmllbGQtaWNvbiB7DQogIGNvbG9yOiAjNDA5ZWZmOw0KICBmb250LXNpemU6IDE2cHg7DQp9DQoNCi5maWVsZC1sYWJlbCB7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQouZmllbGQtdHlwZSB7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBmb250LXNpemU6IDEycHg7DQp9DQoNCi5lbXB0eS1mb3JtLCAubm8tZW5hYmxlZC1jb25maWcgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDQwcHggMjBweDsNCiAgY29sb3I6ICM5MDkzOTk7DQp9DQoNCi5lbXB0eS1mb3JtIGksIC5uby1lbmFibGVkLWNvbmZpZyBpIHsNCiAgZm9udC1zaXplOiA0OHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBkaXNwbGF5OiBibG9jazsNCn0NCg0KLmNvbmZpZy1saXN0LWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDhweDsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLmxpc3QtaGVhZGVyIHsNCiAgcGFkZGluZzogMjBweCAyNHB4Ow0KICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQoubGlzdC1oZWFkZXIgaDQgew0KICBtYXJnaW46IDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi5mb3JtLWZpZWxkcy1jb25maWcgew0KICBtYXgtaGVpZ2h0OiA2MDBweDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCn0NCg0KLmZvcm0tZmllbGRzLXRvb2xiYXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIHBhZGRpbmc6IDE2cHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQoudG9vbGJhci1sZWZ0IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAxMnB4Ow0KfQ0KDQouZm9ybS1maWVsZHMtbGlzdCB7DQogIHBhZGRpbmc6IDE2cHg7DQp9DQoNCi5lbXB0eS1maWVsZHMgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDQwcHggMjBweDsNCiAgY29sb3I6ICM5MDkzOTk7DQp9DQoNCi5lbXB0eS1maWVsZHMgaSB7DQogIGZvbnQtc2l6ZTogNDhweDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgZGlzcGxheTogYmxvY2s7DQp9DQoNCi5maWVsZC1jb25maWctaXRlbSB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgbWFyZ2luLWJvdHRvbTogMTJweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLmZpZWxkLWNvbmZpZy1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDEycHg7DQogIHBhZGRpbmc6IDEycHggMTZweDsNCiAgYmFja2dyb3VuZDogI2ZhZmJmYzsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7DQp9DQoNCi5maWVsZC1pbmRleCB7DQogIGRpc3BsYXk6IGlubGluZS1mbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgd2lkdGg6IDI0cHg7DQogIGhlaWdodDogMjRweDsNCiAgYmFja2dyb3VuZDogIzQwOWVmZjsNCiAgY29sb3I6IHdoaXRlOw0KICBib3JkZXItcmFkaXVzOiA1MCU7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQoNCi5maWVsZC1jb25maWctYm9keSB7DQogIHBhZGRpbmc6IDEycHggMTZweDsNCn0NCg0KLnByZXZpZXctaGVhZGVyIHsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAyNHB4Ow0KICBwYWRkaW5nLWJvdHRvbTogMTZweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCi5wcmV2aWV3LWhlYWRlciBoMyB7DQogIG1hcmdpbjogMCAwIDhweCAwOw0KICBjb2xvcjogIzMwMzEzMzsNCiAgZm9udC1zaXplOiAxOHB4Ow0KfQ0KDQoucHJldmlldy1oZWFkZXIgcCB7DQogIG1hcmdpbjogMDsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLnByZXZpZXctZmllbGQgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQoucHJldmlldy1sYWJlbCB7DQogIGRpc3BsYXk6IGJsb2NrOw0KICBtYXJnaW4tYm90dG9tOiA4cHg7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQoucmVxdWlyZWQgew0KICBjb2xvcjogI2Y1NmM2YzsNCiAgbWFyZ2luLWxlZnQ6IDRweDsNCn0NCg0KLnByZXZpZXctaW5wdXQgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLyog6LWe5Yqp5ZWG5Zu+54mH5LiK5Lyg5qC35byPICovDQouc3BvbnNvci11cGxvYWQtY29udGFpbmVyIHsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLmN1cnJlbnQtc3BvbnNvciB7DQogIG1hcmdpbi1ib3R0b206IDMwcHg7DQp9DQoNCi5jdXJyZW50LXNwb25zb3IgaDQgew0KICBtYXJnaW46IDAgMCAxNXB4IDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi5zcG9uc29yLWltYWdlLXByZXZpZXcgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmc6IDIwcHg7DQogIGJvcmRlcjogMnB4IGRhc2hlZCAjZTRlN2VkOw0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIGJhY2tncm91bmQ6ICNmYWZiZmM7DQp9DQoNCi5zcG9uc29yLWltZyB7DQogIG1heC13aWR0aDogMTAwJTsNCiAgbWF4LWhlaWdodDogMjAwcHg7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCn0NCg0KLnVwbG9hZC1zZWN0aW9uIGg0IHsNCiAgbWFyZ2luOiAwIDAgMTVweCAwOw0KICBjb2xvcjogIzMwMzEzMzsNCiAgZm9udC1zaXplOiAxNnB4Ow0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouc3BvbnNvci11cGxvYWRlciB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQoudXBsb2FkLWFyZWEgew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxODBweDsNCiAgYm9yZGVyOiAycHggZGFzaGVkICNkOWQ5ZDk7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIG92ZXJmbG93OiBoaWRkZW47DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBiYWNrZ3JvdW5kOiAjZmFmYmZjOw0KICB0cmFuc2l0aW9uOiBib3JkZXItY29sb3IgMC4zczsNCn0NCg0KLnVwbG9hZC1hcmVhOmhvdmVyIHsNCiAgYm9yZGVyLWNvbG9yOiAjNDA5ZWZmOw0KfQ0KDQouc3BvbnNvci11cGxvYWRlci1pY29uIHsNCiAgZm9udC1zaXplOiAyOHB4Ow0KICBjb2xvcjogIzhjOTM5ZDsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCn0NCg0KLnVwbG9hZC10ZXh0IHsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KfQ0KDQoudXBsb2FkLXRpcCB7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBmb250LXNpemU6IDEycHg7DQp9DQoNCi5zcG9uc29yLWFjdGlvbnMgew0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHBhZGRpbmctdG9wOiAyMHB4Ow0KICBib3JkZXItdG9wOiAxcHggc29saWQgI2U0ZTdlZDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi3CA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/haitang/projectFormConfig", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 表单配置管理区域 -->\r\n    <div class=\"form-config-container\">\r\n      <div class=\"config-header\">\r\n        <h3>项目报名表单配置管理</h3>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"warning\"\r\n            icon=\"el-icon-picture\"\r\n            @click=\"handleSponsorUpload\"\r\n            v-hasPermi=\"['miniapp:haitang:project:edit']\"\r\n          >赞助商图片</el-button>\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"handleAdd\"\r\n            v-hasPermi=\"['miniapp:haitang:formConfig:add']\"\r\n          >新增配置</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"config-content\" v-loading=\"loading\">\r\n        <!-- 当前启用的配置 -->\r\n        <div v-if=\"enabledConfig\" class=\"enabled-config\">\r\n          <div class=\"enabled-header\">\r\n            <h4>\r\n              <i class=\"el-icon-check\" style=\"color: #67c23a;\"></i>\r\n              当前启用配置：{{ enabledConfig.configName }}\r\n            </h4>\r\n            <div class=\"enabled-actions\">\r\n              <el-button\r\n                size=\"small\"\r\n                type=\"primary\"\r\n                icon=\"el-icon-setting\"\r\n                @click=\"handleFormConfig(enabledConfig)\"\r\n                v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\r\n              >配置表单</el-button>\r\n              <el-button\r\n                size=\"small\"\r\n                type=\"success\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handlePreview(enabledConfig)\"\r\n              >预览表单</el-button>\r\n            </div>\r\n          </div>\r\n          <div class=\"enabled-description\">\r\n            <p>{{ enabledConfig.configDescription || '暂无描述' }}</p>\r\n          </div>\r\n          <div v-if=\"enabledFormFields.length > 0\" class=\"form-preview\">\r\n            <h5>表单字段预览：</h5>\r\n            <div class=\"field-list\">\r\n              <div v-for=\"(field, index) in enabledFormFields\" :key=\"index\" class=\"field-item\">\r\n                <div class=\"field-info\">\r\n                  <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\r\n                  <span class=\"field-label\">{{ field.label }}</span>\r\n                  <el-tag size=\"mini\" type=\"success\">{{ field.name }}</el-tag>\r\n                  <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\r\n                  <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\r\n                  <span class=\"field-type\">{{ getFieldTypeName(field.type) }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div v-else class=\"empty-form\">\r\n            <i class=\"el-icon-document-add\"></i>\r\n            <p>该配置暂未设置表单字段，点击\"配置表单\"开始设置</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 无启用配置时的提示 -->\r\n        <div v-else class=\"no-enabled-config\">\r\n          <i class=\"el-icon-warning-outline\"></i>\r\n          <p>暂无启用的表单配置，请先创建并启用一个配置</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 所有配置列表 -->\r\n    <div class=\"config-list-container\">\r\n      <div class=\"list-header\">\r\n        <h4>所有表单配置</h4>\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\">\r\n          <el-form-item label=\"配置名称\" prop=\"configName\">\r\n            <el-input\r\n              v-model=\"queryParams.configName\"\r\n              placeholder=\"请输入配置名称\"\r\n              clearable\r\n              style=\"width: 200px;\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable style=\"width: 120px;\">\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_normal_disable\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <el-table v-loading=\"listLoading\" :data=\"formConfigList\" @selection-change=\"handleSelectionChange\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column label=\"配置名称\" align=\"center\" prop=\"configName\" min-width=\"150\" />\r\n        <el-table-column label=\"配置描述\" align=\"center\" prop=\"configDescription\" show-overflow-tooltip min-width=\"200\" />\r\n        <el-table-column label=\"是否启用\" align=\"center\" prop=\"isEnabled\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.isEnabled === '1'\" type=\"success\">已启用</el-tag>\r\n            <el-tag v-else type=\"info\">未启用</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-setting\"\r\n              @click=\"handleFormConfig(scope.row)\"\r\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\r\n            >配置</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\r\n            >编辑</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-check\"\r\n              @click=\"handleEnable(scope.row)\"\r\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\r\n              v-if=\"scope.row.isEnabled !== '1'\"\r\n            >启用</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              v-hasPermi=\"['miniapp:haitang:formConfig:remove']\"\r\n            >删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 添加或修改配置基本信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-form-item label=\"配置名称\" prop=\"configName\">\r\n          <el-input v-model=\"form.configName\" placeholder=\"请输入配置名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"配置描述\" prop=\"configDescription\">\r\n          <el-input v-model=\"form.configDescription\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入配置描述\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"sortOrder\">\r\n          <el-input-number v-model=\"form.sortOrder\" controls-position=\"right\" :min=\"0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"status\">\r\n          <el-radio-group v-model=\"form.status\">\r\n            <el-radio\r\n              v-for=\"dict in dict.type.sys_normal_disable\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.value\"\r\n            >{{dict.label}}</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单配置对话框 -->\r\n    <el-dialog title=\"表单字段配置\" :visible.sync=\"formConfigOpen\" width=\"1000px\" append-to-body>\r\n      <div class=\"form-fields-config\">\r\n        <!-- 工具栏 -->\r\n        <div class=\"form-fields-toolbar\">\r\n          <div class=\"toolbar-left\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">\r\n              添加字段\r\n            </el-button>\r\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"small\">\r\n              <el-button size=\"small\">\r\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n              </el-button>\r\n              <el-dropdown-menu slot=\"dropdown\">\r\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\r\n                <el-dropdown-item command=\"project\">项目报名模板</el-dropdown-item>\r\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </el-dropdown>\r\n          </div>\r\n          <div class=\"toolbar-right\">\r\n            <el-button size=\"small\" @click=\"handlePreviewForm\" icon=\"el-icon-view\">预览表单</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 字段配置列表 -->\r\n        <div class=\"form-fields-list\">\r\n          <div v-if=\"currentFormFields.length === 0\" class=\"empty-fields\">\r\n            <i class=\"el-icon-document-add\"></i>\r\n            <p>暂无字段，点击\"添加字段\"开始配置</p>\r\n          </div>\r\n          <div v-else>\r\n            <div v-for=\"(field, index) in currentFormFields\" :key=\"index\" class=\"field-config-item\">\r\n              <div class=\"field-config-header\">\r\n                <span class=\"field-index\">{{ index + 1 }}</span>\r\n                <el-input v-model=\"field.label\" placeholder=\"字段标签\" size=\"small\" style=\"width: 150px;\" />\r\n                <el-input v-model=\"field.name\" placeholder=\"字段名称\" size=\"small\" style=\"width: 120px;\" />\r\n                <el-select v-model=\"field.type\" placeholder=\"字段类型\" size=\"small\" style=\"width: 140px;\" @change=\"handleFieldTypeChange(field)\">\r\n                  <el-option label=\"📝 文本输入\" value=\"input\" />\r\n                  <el-option label=\"📄 多行文本\" value=\"textarea\" />\r\n                  <el-option label=\"🔢 数字输入\" value=\"number\" />\r\n                  <el-option label=\"📧 邮箱\" value=\"email\" />\r\n                  <el-option label=\"📞 电话\" value=\"tel\" />\r\n                  <el-option label=\"🔘 单选\" value=\"radio\" />\r\n                  <el-option label=\"☑️ 多选\" value=\"checkbox\" />\r\n                  <el-option label=\"📋 下拉选择\" value=\"select\" />\r\n                  <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\r\n                  <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\r\n                  <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\r\n                  <el-option label=\"🎡 滚动单选\" value=\"picker\" />\r\n                  <el-option label=\"📅 日期\" value=\"date\" />\r\n                  <el-option label=\"📎 文件上传\" value=\"file\" />\r\n                </el-select>\r\n                <el-checkbox v-model=\"field.required\" size=\"small\">必填</el-checkbox>\r\n                <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"removeFormField(index)\">删除</el-button>\r\n              </div>\r\n              <div class=\"field-config-body\" v-if=\"needOptions(field.type)\">\r\n                <!-- 滚动单选特殊配置 -->\r\n                <div v-if=\"field.type === 'picker'\" class=\"picker-options-config\">\r\n                  <div class=\"picker-options-header\">\r\n                    <span>选项配置（支持禁用选项）</span>\r\n                    <el-button size=\"mini\" type=\"primary\" @click=\"addPickerOption(field)\">添加选项</el-button>\r\n                  </div>\r\n                  <div class=\"picker-options-list\">\r\n                    <div v-for=\"(option, optIndex) in getPickerOptionsForEdit(field)\" :key=\"optIndex\" class=\"picker-option-item\">\r\n                      <el-input v-model=\"option.text\" placeholder=\"选项内容\" size=\"small\" style=\"width: 200px;\" @input=\"updatePickerOptionsFromEdit(field)\" />\r\n                      <el-checkbox v-model=\"option.disabled\" size=\"small\" @change=\"updatePickerOptionsFromEdit(field)\">禁用</el-checkbox>\r\n                      <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"removePickerOption(field, optIndex)\">删除</el-button>\r\n                    </div>\r\n                  </div>\r\n                  <div v-if=\"getPickerOptionsForEdit(field).length === 0\" class=\"empty-picker-options\">\r\n                    <span>暂无选项，点击\"添加选项\"开始配置</span>\r\n                  </div>\r\n                </div>\r\n                <!-- 其他字段类型的普通配置 -->\r\n                <el-input\r\n                  v-else\r\n                  v-model=\"field.options\"\r\n                  :placeholder=\"getOptionsPlaceholder(field.type)\"\r\n                  size=\"small\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"saveFormConfig\">保存配置</el-button>\r\n        <el-button @click=\"formConfigOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单预览对话框 -->\r\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\r\n      <div class=\"form-preview\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ currentConfig ? currentConfig.configName : '项目报名表' }}</h3>\r\n          <p>请填写以下信息完成报名</p>\r\n        </div>\r\n        <div class=\"preview-form\">\r\n          <div v-for=\"(field, index) in currentFormFields\" :key=\"index\" class=\"preview-field\">\r\n            <label class=\"preview-label\">\r\n              {{ field.label }}\r\n              <span v-if=\"field.required\" class=\"required\">*</span>\r\n            </label>\r\n            <div class=\"preview-input\">\r\n              <!-- 基础输入类型 -->\r\n              <el-input\r\n                v-if=\"['input', 'email', 'tel'].includes(field.type)\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <el-input\r\n                v-else-if=\"field.type === 'textarea'\"\r\n                type=\"textarea\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <!-- 数字类型 -->\r\n              <el-input-number\r\n                v-else-if=\"field.type === 'number'\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <!-- 单选类型 -->\r\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\r\n                <el-radio\r\n                  v-for=\"option in getFieldOptions(field.options)\"\r\n                  :key=\"option\"\r\n                  :label=\"option\"\r\n                >{{ option }}</el-radio>\r\n              </el-radio-group>\r\n              <!-- 单选+其他 -->\r\n              <div v-else-if=\"field.type === 'radio_other'\">\r\n                <el-radio-group disabled style=\"margin-bottom: 8px;\">\r\n                  <el-radio\r\n                    v-for=\"option in getFieldOptions(field.options)\"\r\n                    :key=\"option\"\r\n                    :label=\"option\"\r\n                  >{{ option }}</el-radio>\r\n                  <el-radio label=\"其他\">其他</el-radio>\r\n                </el-radio-group>\r\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\r\n              </div>\r\n              <!-- 多选类型 -->\r\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\r\n                <el-checkbox\r\n                  v-for=\"option in getFieldOptions(field.options)\"\r\n                  :key=\"option\"\r\n                  :label=\"option\"\r\n                >{{ option }}</el-checkbox>\r\n              </el-checkbox-group>\r\n              <!-- 多选+其他 -->\r\n              <div v-else-if=\"field.type === 'checkbox_other'\">\r\n                <el-checkbox-group disabled style=\"margin-bottom: 8px;\">\r\n                  <el-checkbox\r\n                    v-for=\"option in getFieldOptions(field.options)\"\r\n                    :key=\"option\"\r\n                    :label=\"option\"\r\n                  >{{ option }}</el-checkbox>\r\n                  <el-checkbox label=\"其他\">其他</el-checkbox>\r\n                </el-checkbox-group>\r\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\r\n              </div>\r\n              <!-- 下拉选择 -->\r\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled>\r\n                <el-option\r\n                  v-for=\"option in getFieldOptions(field.options)\"\r\n                  :key=\"option\"\r\n                  :label=\"option\"\r\n                  :value=\"option\"\r\n                />\r\n              </el-select>\r\n              <!-- 下拉+其他 -->\r\n              <div v-else-if=\"field.type === 'select_other'\">\r\n                <el-select :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"margin-bottom: 8px; width: 100%;\">\r\n                  <el-option\r\n                    v-for=\"option in getFieldOptions(field.options)\"\r\n                    :key=\"option\"\r\n                    :label=\"option\"\r\n                    :value=\"option\"\r\n                  />\r\n                  <el-option label=\"其他\" value=\"其他\" />\r\n                </el-select>\r\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\r\n              </div>\r\n              <!-- 滚动单选 -->\r\n              <div v-else-if=\"field.type === 'picker'\" class=\"picker-preview\">\r\n                <div class=\"picker-display\">\r\n                  <span class=\"picker-placeholder\">{{ '请选择' + field.label }}</span>\r\n                  <i class=\"el-icon-arrow-down\"></i>\r\n                </div>\r\n                <div class=\"picker-options-preview\">\r\n                  <div v-for=\"(option, index) in getPickerOptions(field.options)\" :key=\"index\"\r\n                       :class=\"['picker-option-preview', { 'disabled': option.disabled }]\">\r\n                    {{ option.text }}\r\n                    <span v-if=\"option.disabled\" class=\"disabled-tag\">禁用</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- 日期类型 -->\r\n              <el-date-picker\r\n                v-else-if=\"field.type === 'date'\"\r\n                type=\"date\"\r\n                :placeholder=\"'请选择' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <!-- 文件上传 -->\r\n              <el-upload\r\n                v-else-if=\"field.type === 'file'\"\r\n                class=\"upload-demo\"\r\n                action=\"#\"\r\n                :auto-upload=\"false\"\r\n                disabled\r\n              >\r\n                <el-button size=\"small\" type=\"primary\" disabled>点击上传</el-button>\r\n              </el-upload>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 赞助商图片上传对话框 -->\r\n    <el-dialog title=\"赞助商图片管理\" :visible.sync=\"sponsorDialogVisible\" width=\"600px\" append-to-body>\r\n      <div class=\"sponsor-upload-container\">\r\n        <div class=\"current-sponsor\" v-if=\"currentSponsorImage\">\r\n          <h4>当前赞助商图片</h4>\r\n          <div class=\"sponsor-image-preview\">\r\n            <img :src=\"currentSponsorImage\" alt=\"赞助商图片\" class=\"sponsor-img\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"upload-section\">\r\n          <h4>{{ currentSponsorImage ? '更换赞助商图片' : '上传赞助商图片' }}</h4>\r\n          <el-upload\r\n            class=\"sponsor-uploader\"\r\n            :action=\"uploadAction\"\r\n            :headers=\"uploadHeaders\"\r\n            :show-file-list=\"false\"\r\n            :on-success=\"handleSponsorUploadSuccess\"\r\n            :on-error=\"handleSponsorUploadError\"\r\n            :before-upload=\"beforeSponsorUpload\"\r\n            accept=\"image/*\"\r\n          >\r\n            <div class=\"upload-area\">\r\n              <i class=\"el-icon-plus sponsor-uploader-icon\"></i>\r\n              <div class=\"upload-text\">点击上传图片</div>\r\n              <div class=\"upload-tip\">支持 JPG、PNG 格式，建议尺寸 400x200px</div>\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n\r\n        <div class=\"sponsor-actions\" v-if=\"currentSponsorImage\">\r\n          <el-button type=\"danger\" @click=\"handleDeleteSponsor\">删除当前图片</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"sponsorDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listFormConfig, getFormConfig, delFormConfig, addFormConfig, updateFormConfig, enableFormConfig, getSponsorImage, updateSponsorImage } from \"@/api/miniapp/haitang/formConfig\";\r\nimport request from '@/utils/request';\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"FormConfig\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      listLoading: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 天大海棠杯项目报名表单配置表格数据\r\n      formConfigList: [],\r\n      // 当前启用的配置\r\n      enabledConfig: null,\r\n      // 启用配置的表单字段\r\n      enabledFormFields: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示表单配置弹出层\r\n      formConfigOpen: false,\r\n      // 是否显示表单预览弹出层\r\n      previewDialogVisible: false,\r\n      // 当前配置的表单字段\r\n      currentFormFields: [],\r\n      // 当前操作的配置\r\n      currentConfig: null,\r\n      // 滚动单选字段的编辑数据缓存\r\n      pickerEditData: {},\r\n      // 赞助商图片相关\r\n      sponsorDialogVisible: false,\r\n      currentSponsorImage: '',\r\n      uploadAction: process.env.VUE_APP_BASE_API + \"/common/upload\",\r\n      uploadHeaders: { Authorization: \"Bearer \" + getToken() },\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        configName: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        configName: [\r\n          { required: true, message: \"配置名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.loadEnabledConfig();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 加载启用的配置 */\r\n    loadEnabledConfig() {\r\n      this.loading = true;\r\n      request({\r\n        url: '/miniapp/haitang/formConfig/enabled',\r\n        method: 'get'\r\n      }).then(response => {\r\n        if (response.data) {\r\n          this.enabledConfig = response.data;\r\n          this.loadEnabledFormFields();\r\n        } else {\r\n          this.enabledConfig = null;\r\n          this.enabledFormFields = [];\r\n        }\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.enabledConfig = null;\r\n        this.enabledFormFields = [];\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 加载启用配置的表单字段 */\r\n    loadEnabledFormFields() {\r\n      if (this.enabledConfig && this.enabledConfig.formConfig) {\r\n        try {\r\n          this.enabledFormFields = JSON.parse(this.enabledConfig.formConfig);\r\n        } catch (e) {\r\n          this.enabledFormFields = [];\r\n        }\r\n      } else {\r\n        this.enabledFormFields = [];\r\n      }\r\n    },\r\n    /** 查询天大海棠杯项目报名表单配置列表 */\r\n    getList() {\r\n      this.listLoading = true;\r\n      listFormConfig(this.queryParams).then(response => {\r\n        this.formConfigList = response.rows;\r\n        this.total = response.total;\r\n        this.listLoading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        configId: null,\r\n        configName: null,\r\n        configDescription: null,\r\n        formConfig: null,\r\n        isEnabled: \"0\",\r\n        sortOrder: 0,\r\n        status: \"0\",\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.configId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加天大海棠杯项目报名表单配置\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const configId = row.configId || this.ids\r\n      getFormConfig(configId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改天大海棠杯项目报名表单配置\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.configId != null) {\r\n            updateFormConfig(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addFormConfig(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const configIds = row.configId || this.ids;\r\n      this.$modal.confirm('是否确认删除天大海棠杯项目报名表单配置编号为\"' + configIds + '\"的数据项？').then(function() {\r\n        return delFormConfig(configIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 表单配置按钮操作 */\r\n    handleFormConfig(row) {\r\n      this.currentConfig = row;\r\n      this.currentFormFields = [];\r\n      this.pickerEditData = {}; // 清空编辑数据缓存\r\n\r\n      if (row.formConfig) {\r\n        try {\r\n          this.currentFormFields = JSON.parse(row.formConfig);\r\n          // 初始化滚动单选字段的编辑数据\r\n          this.currentFormFields.forEach(field => {\r\n            if (field.type === 'picker' && field.options) {\r\n              const fieldKey = field.name || 'temp_' + Date.now();\r\n              this.pickerEditData[fieldKey] = this.getPickerOptions(field.options);\r\n            }\r\n          });\r\n        } catch (e) {\r\n          this.currentFormFields = [];\r\n        }\r\n      }\r\n      this.formConfigOpen = true;\r\n    },\r\n    /** 预览按钮操作 */\r\n    handlePreview(row) {\r\n      this.currentConfig = row;\r\n      this.currentFormFields = [];\r\n      if (row.formConfig) {\r\n        try {\r\n          this.currentFormFields = JSON.parse(row.formConfig);\r\n        } catch (e) {\r\n          this.currentFormFields = [];\r\n        }\r\n      }\r\n      this.previewDialogVisible = true;\r\n    },\r\n    /** 启用按钮操作 */\r\n    handleEnable(row) {\r\n      this.$modal.confirm('启用此配置将禁用其他所有配置，是否确认启用？').then(function() {\r\n        return enableFormConfig(row.configId);\r\n      }).then(() => {\r\n        this.loadEnabledConfig();\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"启用成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 添加表单字段 */\r\n    addFormField() {\r\n      const defaultName = this.generateUniqueFieldName('field');\r\n      const newField = {\r\n        name: defaultName,\r\n        label: '',\r\n        type: 'input',\r\n        required: false,\r\n        options: ''\r\n      };\r\n      this.currentFormFields.push(newField);\r\n    },\r\n    /** 删除表单字段 */\r\n    removeFormField(index) {\r\n      this.currentFormFields.splice(index, 1);\r\n    },\r\n    /** 生成唯一字段名 */\r\n    generateUniqueFieldName(prefix) {\r\n      let counter = 1;\r\n      let name = prefix + counter;\r\n      while (this.currentFormFields.some(field => field.name === name)) {\r\n        counter++;\r\n        name = prefix + counter;\r\n      }\r\n      return name;\r\n    },\r\n    /** 判断字段类型是否需要选项 */\r\n    needOptions(type) {\r\n      return ['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other', 'picker'].includes(type);\r\n    },\r\n    /** 处理模板命令 */\r\n    handleTemplateCommand(command) {\r\n      if (command === 'clear') {\r\n        this.$modal.confirm('确认清空所有字段？').then(() => {\r\n          this.currentFormFields = [];\r\n        });\r\n        return;\r\n      }\r\n\r\n      const templates = {\r\n        basic: [\r\n          { label: '姓名', name: '', type: 'input', required: true, options: '' },\r\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\r\n          { label: '邮箱地址', name: '', type: 'email', required: false, options: '' }\r\n        ],\r\n        project: [\r\n          { label: '项目名称', name: '', type: 'input', required: true, options: '' },\r\n          { label: '团队负责人', name: '', type: 'input', required: true, options: '' },\r\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\r\n          { label: '邮箱地址', name: '', type: 'email', required: true, options: '' },\r\n          { label: '项目简介', name: '', type: 'textarea', required: true, options: '' },\r\n          { label: '项目类型', name: '', type: 'select_other', required: true, options: '科技创新,商业模式,社会公益,文化创意' },\r\n          { label: '团队规模', name: '', type: 'radio', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\r\n          { label: '项目阶段', name: '', type: 'picker', required: true, options: '[{\"text\":\"创意阶段\",\"disabled\":false},{\"text\":\"初创阶段\",\"disabled\":false},{\"text\":\"成长阶段\",\"disabled\":false},{\"text\":\"成熟阶段\",\"disabled\":true}]' },\r\n          { label: '报名日期', name: '', type: 'date', required: false, options: '' },\r\n          { label: '项目计划书', name: '', type: 'file', required: true, options: '' },\r\n          { label: '演示视频', name: '', type: 'file', required: false, options: '' }\r\n        ]\r\n      };\r\n\r\n      if (templates[command]) {\r\n        this.currentFormFields = templates[command].map(field => ({\r\n          ...field,\r\n          name: this.generateUniqueFieldName(field.label.toLowerCase())\r\n        }));\r\n      }\r\n    },\r\n    /** 预览表单 */\r\n    handlePreviewForm() {\r\n      this.previewDialogVisible = true;\r\n    },\r\n    /** 保存表单配置 */\r\n    saveFormConfig() {\r\n      if (!this.currentConfig) {\r\n        this.$modal.msgError(\"请先选择要配置的表单\");\r\n        return;\r\n      }\r\n\r\n      // 验证字段配置\r\n      for (let i = 0; i < this.currentFormFields.length; i++) {\r\n        const field = this.currentFormFields[i];\r\n        if (!field.label) {\r\n          this.$modal.msgError(`第${i + 1}个字段的标签不能为空`);\r\n          return;\r\n        }\r\n        if (!field.name) {\r\n          field.name = this.generateUniqueFieldName(field.label.toLowerCase());\r\n        }\r\n        if (this.needOptions(field.type) && !field.options) {\r\n          this.$modal.msgError(`第${i + 1}个字段\"${field.label}\"需要设置选项内容`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      const formData = {\r\n        configId: this.currentConfig.configId,\r\n        formConfig: JSON.stringify(this.currentFormFields)\r\n      };\r\n\r\n      updateFormConfig(formData).then(response => {\r\n        this.$modal.msgSuccess(\"表单配置保存成功\");\r\n        this.formConfigOpen = false;\r\n        this.loadEnabledConfig();\r\n        this.getList();\r\n      });\r\n    },\r\n    /** 获取字段选项 */\r\n    getFieldOptions(options) {\r\n      if (!options) return [];\r\n      return options.split(',').map(opt => opt.trim()).filter(opt => opt);\r\n    },\r\n    /** 获取滚动单选选项 */\r\n    getPickerOptions(options) {\r\n      if (!options) return [];\r\n      try {\r\n        const parsed = JSON.parse(options);\r\n        // 确保返回的是数组格式\r\n        return Array.isArray(parsed) ? parsed : [];\r\n      } catch (e) {\r\n        // 如果解析失败，尝试按逗号分隔的格式处理\r\n        if (typeof options === 'string' && options.trim()) {\r\n          return options.split(',').map(opt => ({\r\n            text: opt.trim(),\r\n            disabled: false\r\n          })).filter(opt => opt.text);\r\n        }\r\n        return [];\r\n      }\r\n    },\r\n    /** 添加滚动单选选项 */\r\n    addPickerOption(field) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n      if (!this.pickerEditData[fieldKey]) {\r\n        this.pickerEditData[fieldKey] = this.getPickerOptions(field.options);\r\n      }\r\n      this.pickerEditData[fieldKey].push({\r\n        text: '',\r\n        disabled: false\r\n      });\r\n      this.updatePickerOptionsFromEdit(field);\r\n    },\r\n    /** 删除滚动单选选项 */\r\n    removePickerOption(field, index) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n      if (this.pickerEditData[fieldKey]) {\r\n        this.pickerEditData[fieldKey].splice(index, 1);\r\n        this.updatePickerOptionsFromEdit(field);\r\n      }\r\n    },\r\n    /** 获取用于编辑的滚动单选选项 */\r\n    getPickerOptionsForEdit(field) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n      if (!this.pickerEditData[fieldKey]) {\r\n        // 初始化编辑数据\r\n        this.pickerEditData[fieldKey] = this.getPickerOptions(field.options);\r\n      }\r\n      return this.pickerEditData[fieldKey];\r\n    },\r\n    /** 从编辑数据更新滚动单选选项 */\r\n    updatePickerOptionsFromEdit(field) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n      if (this.pickerEditData[fieldKey]) {\r\n        field.options = JSON.stringify(this.pickerEditData[fieldKey]);\r\n      }\r\n    },\r\n    /** 更新滚动单选选项 */\r\n    updatePickerOptions(field) {\r\n      // 延迟更新，确保数据已经变更\r\n      this.$nextTick(() => {\r\n        let pickerOptions = this.getPickerOptions(field.options);\r\n        field.options = JSON.stringify(pickerOptions);\r\n      });\r\n    },\r\n    /** 处理字段类型变更 */\r\n    handleFieldTypeChange(field) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n\r\n      // 当切换到滚动单选时，如果没有选项则初始化\r\n      if (field.type === 'picker') {\r\n        if (!field.options || field.options === '') {\r\n          const defaultOptions = [\r\n            { text: '选项1', disabled: false },\r\n            { text: '选项2', disabled: false }\r\n          ];\r\n          field.options = JSON.stringify(defaultOptions);\r\n          this.pickerEditData[fieldKey] = defaultOptions;\r\n        } else {\r\n          // 重新初始化编辑数据\r\n          this.pickerEditData[fieldKey] = this.getPickerOptions(field.options);\r\n        }\r\n      }\r\n      // 当切换到其他需要选项的字段类型时，如果是JSON格式则转换为逗号分隔\r\n      else if (this.needOptions(field.type) && field.type !== 'picker' && field.options) {\r\n        // 清除编辑数据缓存\r\n        delete this.pickerEditData[fieldKey];\r\n\r\n        try {\r\n          const parsed = JSON.parse(field.options);\r\n          if (Array.isArray(parsed)) {\r\n            field.options = parsed.map(opt => opt.text || opt.label || opt).join(',');\r\n          }\r\n        } catch (e) {\r\n          // 如果不是JSON格式，保持原样\r\n        }\r\n      } else {\r\n        // 清除编辑数据缓存\r\n        delete this.pickerEditData[fieldKey];\r\n      }\r\n    },\r\n    /** 获取选项输入框占位符 */\r\n    getOptionsPlaceholder(type) {\r\n      const placeholders = {\r\n        radio: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\r\n        checkbox: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\r\n        select: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\r\n        radio_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\r\n        checkbox_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\r\n        select_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\r\n        picker: '滚动单选选项，支持禁用功能，请使用上方的选项配置器进行设置'\r\n      };\r\n      return placeholders[type] || '选项内容，多个选项用逗号分隔';\r\n    },\r\n    /** 获取字段图标 */\r\n    getFieldIcon(type) {\r\n      const icons = {\r\n        input: 'el-icon-edit',\r\n        textarea: 'el-icon-document',\r\n        number: 'el-icon-s-data',\r\n        email: 'el-icon-message',\r\n        tel: 'el-icon-phone',\r\n        radio: 'el-icon-circle-check',\r\n        checkbox: 'el-icon-check',\r\n        select: 'el-icon-arrow-down',\r\n        radio_other: 'el-icon-circle-plus-outline',\r\n        checkbox_other: 'el-icon-circle-plus-outline',\r\n        select_other: 'el-icon-circle-plus-outline',\r\n        picker: 'el-icon-sort',\r\n        date: 'el-icon-date',\r\n        file: 'el-icon-upload'\r\n      };\r\n      return icons[type] || 'el-icon-edit';\r\n    },\r\n    /** 获取字段类型名称 */\r\n    getFieldTypeName(type) {\r\n      const names = {\r\n        input: '文本输入',\r\n        textarea: '多行文本',\r\n        number: '数字输入',\r\n        email: '邮箱',\r\n        tel: '电话',\r\n        radio: '单选',\r\n        checkbox: '多选',\r\n        select: '下拉选择',\r\n        radio_other: '单选+其他',\r\n        checkbox_other: '多选+其他',\r\n        select_other: '下拉+其他',\r\n        picker: '滚动单选',\r\n        date: '日期',\r\n        file: '文件上传'\r\n      };\r\n      return names[type] || '未知类型';\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/haitang/formConfig/export', {\r\n        ...this.queryParams\r\n      }, `formConfig_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 赞助商图片上传按钮操作 */\r\n    handleSponsorUpload() {\r\n      this.loadSponsorImage();\r\n      this.sponsorDialogVisible = true;\r\n    },\r\n    /** 加载赞助商图片 */\r\n    loadSponsorImage() {\r\n      getSponsorImage().then(response => {\r\n        if (response.data && response.data.sponsorUnit) {\r\n          this.currentSponsorImage = response.data.sponsorUnit;\r\n        } else {\r\n          this.currentSponsorImage = '';\r\n        }\r\n      }).catch(() => {\r\n        this.currentSponsorImage = '';\r\n      });\r\n    },\r\n    /** 上传前校验 */\r\n    beforeSponsorUpload(file) {\r\n      const isImage = file.type.indexOf('image/') === 0;\r\n      const isLt5M = file.size / 1024 / 1024 < 5;\r\n\r\n      if (!isImage) {\r\n        this.$modal.msgError('上传文件只能是图片格式!');\r\n        return false;\r\n      }\r\n      if (!isLt5M) {\r\n        this.$modal.msgError('上传图片大小不能超过 5MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    /** 上传成功回调 */\r\n    handleSponsorUploadSuccess(response) {\r\n      if (response.code === 200) {\r\n        const imageUrl = response.url;\r\n        updateSponsorImage(imageUrl).then(() => {\r\n          this.currentSponsorImage = imageUrl;\r\n          this.$modal.msgSuccess('赞助商图片上传成功');\r\n        }).catch(() => {\r\n          this.$modal.msgError('保存赞助商图片失败');\r\n        });\r\n      } else {\r\n        this.$modal.msgError('图片上传失败：' + response.msg);\r\n      }\r\n    },\r\n    /** 上传失败回调 */\r\n    handleSponsorUploadError() {\r\n      this.$modal.msgError('图片上传失败，请重试');\r\n    },\r\n    /** 删除赞助商图片 */\r\n    handleDeleteSponsor() {\r\n      this.$modal.confirm('确认删除当前赞助商图片？').then(() => {\r\n        updateSponsorImage('').then(() => {\r\n          this.currentSponsorImage = '';\r\n          this.$modal.msgSuccess('赞助商图片删除成功');\r\n        }).catch(() => {\r\n          this.$modal.msgError('删除赞助商图片失败');\r\n        });\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-config-container {\r\n  margin-bottom: 20px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.config-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.config-header h3 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.config-content {\r\n  padding: 24px;\r\n  min-height: 200px;\r\n}\r\n\r\n.enabled-config {\r\n  border: 1px solid #67c23a;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  background: #f0f9ff;\r\n}\r\n\r\n.enabled-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.enabled-header h4 {\r\n  margin: 0;\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.enabled-description {\r\n  margin-bottom: 16px;\r\n  color: #606266;\r\n}\r\n\r\n.form-preview h5 {\r\n  margin: 0 0 12px 0;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.field-list {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-item {\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  background: #fafbfc;\r\n}\r\n\r\n.field-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.field-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.field-icon {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.field-label {\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.field-type {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.empty-form, .no-enabled-config {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-form i, .no-enabled-config i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.config-list-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.list-header {\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.list-header h4 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.form-fields-config {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-fields-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.form-fields-list {\r\n  padding: 16px;\r\n}\r\n\r\n.empty-fields {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-fields i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.field-config-item {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  margin-bottom: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-config-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 16px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.field-index {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: #409eff;\r\n  color: white;\r\n  border-radius: 50%;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.field-config-body {\r\n  padding: 12px 16px;\r\n}\r\n\r\n.preview-header {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n}\r\n\r\n.preview-header p {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.preview-field {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.required {\r\n  color: #f56c6c;\r\n  margin-left: 4px;\r\n}\r\n\r\n.preview-input {\r\n  width: 100%;\r\n}\r\n\r\n/* 滚动单选配置样式 */\r\n.picker-options-config {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  padding: 12px;\r\n  background: #fafbfc;\r\n}\r\n\r\n.picker-options-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.picker-options-list {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.picker-option-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 8px;\r\n  padding: 8px;\r\n  background: #fff;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n}\r\n\r\n.picker-option-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.empty-picker-options {\r\n  text-align: center;\r\n  padding: 20px;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 滚动单选预览样式 */\r\n.picker-preview {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.picker-display {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  background: #fff;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.picker-placeholder {\r\n  color: #c0c4cc;\r\n  font-size: 14px;\r\n}\r\n\r\n.picker-options-preview {\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.picker-option-preview {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.picker-option-preview:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.picker-option-preview.disabled {\r\n  color: #c0c4cc;\r\n  background: #f5f7fa;\r\n}\r\n\r\n.disabled-tag {\r\n  font-size: 12px;\r\n  color: #f56c6c;\r\n  background: #fef0f0;\r\n  padding: 2px 6px;\r\n  border-radius: 2px;\r\n}\r\n</style>\r\n\r\n<style scoped>\r\n.form-config-container {\r\n  margin-bottom: 20px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.config-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.config-header h3 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.config-content {\r\n  padding: 24px;\r\n  min-height: 200px;\r\n}\r\n\r\n.enabled-config {\r\n  border: 1px solid #67c23a;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  background: #f0f9ff;\r\n}\r\n\r\n.enabled-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.enabled-header h4 {\r\n  margin: 0;\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.enabled-description {\r\n  margin-bottom: 16px;\r\n  color: #606266;\r\n}\r\n\r\n.form-preview h5 {\r\n  margin: 0 0 12px 0;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.field-list {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-item {\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  background: #fafbfc;\r\n}\r\n\r\n.field-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.field-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.field-icon {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.field-label {\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.field-type {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.empty-form, .no-enabled-config {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-form i, .no-enabled-config i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.config-list-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.list-header {\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.list-header h4 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.form-fields-config {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-fields-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.form-fields-list {\r\n  padding: 16px;\r\n}\r\n\r\n.empty-fields {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-fields i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.field-config-item {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  margin-bottom: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-config-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 16px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.field-index {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: #409eff;\r\n  color: white;\r\n  border-radius: 50%;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.field-config-body {\r\n  padding: 12px 16px;\r\n}\r\n\r\n.preview-header {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n}\r\n\r\n.preview-header p {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.preview-field {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.required {\r\n  color: #f56c6c;\r\n  margin-left: 4px;\r\n}\r\n\r\n.preview-input {\r\n  width: 100%;\r\n}\r\n\r\n/* 赞助商图片上传样式 */\r\n.sponsor-upload-container {\r\n  padding: 20px;\r\n}\r\n\r\n.current-sponsor {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.current-sponsor h4 {\r\n  margin: 0 0 15px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.sponsor-image-preview {\r\n  text-align: center;\r\n  padding: 20px;\r\n  border: 2px dashed #e4e7ed;\r\n  border-radius: 6px;\r\n  background: #fafbfc;\r\n}\r\n\r\n.sponsor-img {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.upload-section h4 {\r\n  margin: 0 0 15px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.sponsor-uploader {\r\n  width: 100%;\r\n}\r\n\r\n.upload-area {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #fafbfc;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-area:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.sponsor-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.upload-text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.upload-tip {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.sponsor-actions {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e4e7ed;\r\n}\r\n</style>\r\n"]}]}