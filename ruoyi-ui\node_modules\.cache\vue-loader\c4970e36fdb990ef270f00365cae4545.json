{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\miniapp-test.vue?vue&type=style&index=0&id=37c95021&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\business\\investment\\miniapp-test.vue", "mtime": 1754384021191}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1jb250YWluZXIgew0KICBwYWRkaW5nOiAyMHB4Ow0KfQ0K"}, {"version": 3, "sources": ["miniapp-test.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiKA;AACA;AACA", "file": "miniapp-test.vue", "sourceRoot": "src/views/miniapp/business/investment", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <h2>小程序端项目投资筛选测试</h2>\r\n\r\n    <!-- 筛选条件 -->\r\n    <el-form :model=\"filterParams\" ref=\"filterForm\" size=\"small\" :inline=\"true\">\r\n      <el-form-item label=\"融资轮次\">\r\n        <el-select v-model=\"filterParams.financingRound\" placeholder=\"请选择融资轮次\" clearable>\r\n          <el-option label=\"种子轮\" value=\"种子轮\" />\r\n          <el-option label=\"天使轮\" value=\"天使轮\" />\r\n          <el-option label=\"A轮融资\" value=\"A轮融资\" />\r\n          <el-option label=\"B轮融资\" value=\"B轮融资\" />\r\n          <el-option label=\"C轮融资\" value=\"C轮融资\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"所在地区\">\r\n        <el-select v-model=\"filterParams.region\" placeholder=\"请选择地区\" clearable>\r\n          <el-option label=\"北京\" value=\"北京\" />\r\n          <el-option label=\"上海\" value=\"上海\" />\r\n          <el-option label=\"深圳\" value=\"深圳\" />\r\n          <el-option label=\"天津\" value=\"天津\" />\r\n          <el-option label=\"杭州\" value=\"杭州\" />\r\n          <el-option label=\"广州\" value=\"广州\" />\r\n          <el-option label=\"成都\" value=\"成都\" />\r\n          <el-option label=\"重庆\" value=\"重庆\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"行业\">\r\n        <el-select v-model=\"filterParams.industryId\" placeholder=\"请选择行业\" clearable>\r\n          <el-option label=\"航空航天\" value=\"30\" />\r\n          <el-option label=\"硬科技\" value=\"31\" />\r\n          <el-option label=\"人工智能\" value=\"32\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"关键字\">\r\n        <el-input\r\n          v-model=\"filterParams.keyword\"\r\n          placeholder=\"请输入项目名称或关键字\"\r\n          clearable\r\n          style=\"width: 200px;\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n        <el-button @click=\"handleReset\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <!-- 结果展示 -->\r\n    <el-divider>搜索结果</el-divider>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"investmentList\" border>\r\n      <el-table-column label=\"项目名称\" prop=\"projectName\" width=\"150\" />\r\n      <el-table-column label=\"融资轮次\" prop=\"financingRound\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag type=\"success\">{{ scope.row.financingRound }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"所属行业\" prop=\"industryName\" width=\"120\" />\r\n      <el-table-column label=\"所在地区\" prop=\"region\" width=\"100\" />\r\n      <el-table-column label=\"项目标签\" prop=\"tags\" width=\"200\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            v-for=\"(tag, index) in getTagArray(scope.row.tags)\"\r\n            :key=\"index\"\r\n            size=\"mini\"\r\n            style=\"margin-right: 5px;\"\r\n            type=\"info\"\r\n          >\r\n            {{ tag }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"项目简介\" prop=\"briefIntroduction\" show-overflow-tooltip />\r\n      <el-table-column label=\"联系人\" prop=\"contactPerson\" width=\"100\" />\r\n    </el-table>\r\n\r\n    <div style=\"margin-top: 20px; text-align: center;\">\r\n      <span>共找到 {{ investmentList.length }} 个项目</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listEnabledInvestment } from \"@/api/miniapp/investment\";\r\n\r\nexport default {\r\n  name: \"MiniappInvestmentTest\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      investmentList: [],\r\n      filterParams: {\r\n        financingRound: '',\r\n        region: '',\r\n        industryId: '',\r\n        keyword: ''\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询项目投资列表 */\r\n    getList() {\r\n      this.loading = true;\r\n\r\n      // 构建查询参数，过滤空值\r\n      const params = {};\r\n      if (this.filterParams.financingRound) {\r\n        params.financingRound = this.filterParams.financingRound;\r\n      }\r\n      if (this.filterParams.region) {\r\n        params.region = this.filterParams.region;\r\n      }\r\n      if (this.filterParams.industryId) {\r\n        params.industryId = this.filterParams.industryId;\r\n      }\r\n      if (this.filterParams.keyword) {\r\n        params.keyword = this.filterParams.keyword;\r\n      }\r\n\r\n      listEnabledInvestment(params).then(response => {\r\n        this.investmentList = response.data;\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.loading = false;\r\n      });\r\n    },\r\n\r\n    /** 处理标签数组 */\r\n    getTagArray(tags) {\r\n      if (!tags) return [];\r\n      return tags.split(',').filter(tag => tag.trim() !== '');\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleSearch() {\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    handleReset() {\r\n      this.filterParams = {\r\n        financingRound: '',\r\n        region: '',\r\n        industryId: '',\r\n        keyword: ''\r\n      };\r\n      this.getList();\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  padding: 20px;\r\n}\r\n</style>\r\n"]}]}