{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectRegistration\\index.vue?vue&type=style&index=0&id=8e08a9e6&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectRegistration\\index.vue", "mtime": 1754384840752}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoucmVnaXN0cmF0aW9uLWRldGFpbCB7DQogIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi5iYXNpYy1pbmZvIHsNCiAgbWFyZ2luLWJvdHRvbTogMzBweDsNCn0NCg0KLmJhc2ljLWluZm8gaDMgew0KICBtYXJnaW46IDAgMCAyMHB4IDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQogIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjNDA5ZWZmOw0KICBwYWRkaW5nLWJvdHRvbTogMTBweDsNCn0NCg0KLmluZm8taXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5pbmZvLWl0ZW0gbGFiZWwgew0KICBmb250LXdlaWdodDogNTAwOw0KICBjb2xvcjogIzYwNjI2NjsNCiAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KICBtaW4td2lkdGg6IDgwcHg7DQp9DQoNCi5pbmZvLWl0ZW0gc3BhbiB7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQouZm9ybS1kYXRhIHsNCiAgbWFyZ2luLWJvdHRvbTogMzBweDsNCn0NCg0KLmZvcm0tZGF0YSBoMyB7DQogIG1hcmdpbjogMCAwIDIwcHggMDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICM2N2MyM2E7DQogIHBhZGRpbmctYm90dG9tOiAxMHB4Ow0KfQ0KDQouZmlsZS1kaXNwbGF5IHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5maWxlLWl0ZW0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDhweDsNCiAgcGFkZGluZzogOHB4IDEycHg7DQogIGJhY2tncm91bmQ6ICNmNWY3ZmE7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCn0NCg0KLmZpbGUtbGluayB7DQogIGNvbG9yOiAjNDA5ZWZmOw0KICB0ZXh0LWRlY29yYXRpb246IG5vbmU7DQp9DQoNCi5maWxlLWxpbms6aG92ZXIgew0KICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTsNCn0NCg0KLmFycmF5LWRpc3BsYXkgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGdhcDogNXB4Ow0KfQ0KDQoudGV4dC12YWx1ZSB7DQogIGNvbG9yOiAjMzAzMTMzOw0KICB3b3JkLWJyZWFrOiBicmVhay1hbGw7DQp9DQoNCi5lbXB0eS12YWx1ZSB7DQogIGNvbG9yOiAjYzBjNGNjOw0KICBmb250LXN0eWxlOiBpdGFsaWM7DQp9DQoNCi5yYXctZGF0YSB7DQogIG1hcmdpbi10b3A6IDIwcHg7DQp9DQoNCi5yYXctZGF0YSAuZWwtY29sbGFwc2Ugew0KICBib3JkZXI6IG5vbmU7DQp9DQoNCi5yYXctZGF0YSAuZWwtY29sbGFwc2UtaXRlbV9faGVhZGVyIHsNCiAgYmFja2dyb3VuZDogI2Y1ZjdmYTsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBwYWRkaW5nOiAwIDE1cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICM2MDYyNjY7DQp9DQoNCi5yYXctZGF0YSAuZWwtY29sbGFwc2UtaXRlbV9fY29udGVudCB7DQogIHBhZGRpbmc6IDE1cHggMCAwIDA7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAueA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/haitang/projectRegistration", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"用户姓名\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入用户姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"用户手机号\" prop=\"userPhone\">\r\n        <el-input\r\n          v-model=\"queryParams.userPhone\"\r\n          placeholder=\"请输入用户手机号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:haitang:registration:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:haitang:registration:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:haitang:registration:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"registrationList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"报名ID\" align=\"center\" prop=\"registrationId\" />\r\n      <el-table-column label=\"表单配置\" align=\"center\" prop=\"configName\" />\r\n      <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" />\r\n      <el-table-column label=\"用户手机号\" align=\"center\" prop=\"userPhone\" />\r\n      <el-table-column label=\"报名时间\" align=\"center\" prop=\"registrationTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['miniapp:haitang:registration:query']\"\r\n          >查看</el-button>\r\n\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:haitang:registration:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 查看报名详情对话框 -->\r\n    <el-dialog title=\"报名详情\" :visible.sync=\"viewOpen\" width=\"900px\" append-to-body>\r\n      <div class=\"registration-detail\">\r\n        <!-- 基本信息 -->\r\n        <div class=\"basic-info\">\r\n          <h3>基本信息</h3>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>表单配置：</label>\r\n                <span>{{ viewForm.configName }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>报名时间：</label>\r\n                <span>{{ parseTime(viewForm.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>用户姓名：</label>\r\n                <span>{{ viewForm.userName }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>用户手机号：</label>\r\n                <span>{{ viewForm.userPhone }}</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n\r\n        <!-- 报名表单数据 -->\r\n        <div class=\"form-data\">\r\n          <h3>报名表单数据</h3>\r\n          <el-table :data=\"parsedFormData\" border style=\"width: 100%\">\r\n            <el-table-column prop=\"label\" label=\"字段名称\" width=\"200\" />\r\n            <el-table-column prop=\"value\" label=\"填写内容\" min-width=\"400\">\r\n              <template slot-scope=\"scope\">\r\n                <div v-if=\"scope.row.type === 'file'\" class=\"file-display\">\r\n                  <div v-if=\"scope.row.files && scope.row.files.length > 0\">\r\n                    <div v-for=\"(file, index) in scope.row.files\" :key=\"index\" class=\"file-item\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <a :href=\"file.url\" target=\"_blank\" class=\"file-link\">{{ file.name }}</a>\r\n                    </div>\r\n                  </div>\r\n                  <span v-else class=\"empty-value\">未上传文件</span>\r\n                </div>\r\n                <div v-else-if=\"Array.isArray(scope.row.value)\" class=\"array-display\">\r\n                  <el-tag v-for=\"(item, index) in scope.row.value\" :key=\"index\" size=\"small\" style=\"margin-right: 5px;\">\r\n                    {{ item }}\r\n                  </el-tag>\r\n                </div>\r\n                <span v-else-if=\"scope.row.value !== null && scope.row.value !== undefined && scope.row.value !== ''\" class=\"text-value\">{{ scope.row.value }}</span>\r\n                <span v-else class=\"empty-value\">未填写</span>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 原始数据（可折叠） -->\r\n        <div class=\"raw-data\">\r\n          <el-collapse>\r\n            <el-collapse-item title=\"查看原始JSON数据\" name=\"rawData\">\r\n              <el-input v-model=\"viewForm.formData\" type=\"textarea\" :rows=\"8\" readonly />\r\n            </el-collapse-item>\r\n          </el-collapse>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"viewOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listProjectRegistration, getProjectRegistration, delProjectRegistration } from \"@/api/miniapp/haitang/projectRegistration\";\r\n\r\nexport default {\r\n  name: \"ProjectRegistration\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 天大海棠杯项目报名记录表格数据\r\n      registrationList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userName: null,\r\n        userPhone: null\r\n      },\r\n      // 查看表单参数\r\n      viewForm: {},\r\n      // 是否显示查看弹出层\r\n      viewOpen: false\r\n    };\r\n  },\r\n  computed: {\r\n    /** 解析后的表单数据 */\r\n    parsedFormData() {\r\n      if (!this.viewForm.formData) return [];\r\n\r\n      try {\r\n        const formData = JSON.parse(this.viewForm.formData);\r\n\r\n        // 检查数据格式：如果是数组格式（新格式），直接处理\r\n        if (Array.isArray(formData)) {\r\n          return formData.map(field => ({\r\n            name: field.name,\r\n            label: field.label || this.getDefaultFieldLabel(field.name) || field.name,\r\n            value: this.formatFieldValue(field.value, field.type),\r\n            type: field.type || 'input',\r\n            files: field.type === 'file' ? this.parseFileValue(field.value) : null,\r\n            required: field.required || false,\r\n            options: field.options || ''\r\n          }));\r\n        }\r\n\r\n        // 兼容旧格式：对象格式的数据\r\n        const configData = this.viewForm.configData ? JSON.parse(this.viewForm.configData) : [];\r\n\r\n        // 创建字段配置映射\r\n        const fieldConfigMap = {};\r\n        configData.forEach(field => {\r\n          fieldConfigMap[field.name] = field;\r\n        });\r\n\r\n        // 解析表单数据\r\n        const parsedData = [];\r\n        for (const [fieldName, fieldValue] of Object.entries(formData)) {\r\n          const fieldConfig = fieldConfigMap[fieldName] || {};\r\n\r\n          // 优先使用配置中的中文标签，如果没有则使用字段名\r\n          const displayLabel = fieldConfig.label || this.getDefaultFieldLabel(fieldName) || fieldName;\r\n\r\n          parsedData.push({\r\n            name: fieldName,\r\n            label: displayLabel,\r\n            value: this.formatFieldValue(fieldValue, fieldConfig.type),\r\n            type: fieldConfig.type || 'input',\r\n            files: fieldConfig.type === 'file' ? this.parseFileValue(fieldValue) : null\r\n          });\r\n        }\r\n\r\n        return parsedData;\r\n      } catch (e) {\r\n        console.error('解析表单数据失败:', e);\r\n        return [];\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询天大海棠杯项目报名记录列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listProjectRegistration(this.queryParams).then(response => {\r\n        this.registrationList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.registrationId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      // 这里可以添加修改逻辑，暂时不实现\r\n      this.$modal.msgInfo(\"修改功能暂未实现\");\r\n    },\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      getProjectRegistration(row.registrationId).then(response => {\r\n        this.viewForm = response.data;\r\n        this.viewOpen = true;\r\n      });\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const registrationIds = row.registrationId || this.ids;\r\n      this.$modal.confirm('是否确认删除天大海棠杯项目报名记录编号为\"' + registrationIds + '\"的数据项？').then(function() {\r\n        return delProjectRegistration(registrationIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/haitang/registration/export', {\r\n        ...this.queryParams\r\n      }, `projectRegistration_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 格式化字段值 */\r\n    formatFieldValue(value, fieldType) {\r\n      // 处理空值\r\n      if (value === null || value === undefined || value === '') {\r\n        return '';\r\n      }\r\n\r\n      switch (fieldType) {\r\n        case 'checkbox':\r\n        case 'checkbox_other':\r\n          // 多选值通常是数组\r\n          if (Array.isArray(value)) {\r\n            return value.filter(v => v !== null && v !== undefined && v !== '');\r\n          } else if (typeof value === 'string') {\r\n            // 如果是字符串，尝试按逗号分割\r\n            return value.split(',').map(v => v.trim()).filter(v => v !== '');\r\n          }\r\n          return [value];\r\n        case 'radio':\r\n        case 'radio_other':\r\n        case 'select':\r\n        case 'select_other':\r\n        case 'picker':\r\n          // 单选值\r\n          return String(value);\r\n        case 'file':\r\n          // 文件类型特殊处理\r\n          return value ? '查看文件' : '';\r\n        case 'date':\r\n          // 日期格式化\r\n          return value ? this.parseTime(value, '{y}-{m}-{d}') : '';\r\n        case 'datetime':\r\n          // 日期时间格式化\r\n          return value ? this.parseTime(value, '{y}-{m}-{d} {h}:{i}:{s}') : '';\r\n        case 'number':\r\n          // 数字格式化\r\n          return value ? String(value) : '';\r\n        default:\r\n          return String(value);\r\n      }\r\n    },\r\n    /** 解析文件值 */\r\n    parseFileValue(value) {\r\n      if (!value) return [];\r\n\r\n      try {\r\n        if (typeof value === 'string') {\r\n          // 检查是否是URL格式（以http开头）\r\n          if (value.startsWith('http://') || value.startsWith('https://')) {\r\n            // 从URL中提取文件名\r\n            const fileName = value.split('/').pop() || '文件';\r\n            return [{ name: fileName, url: value }];\r\n          }\r\n\r\n          // 尝试解析JSON字符串\r\n          try {\r\n            const parsed = JSON.parse(value);\r\n            return Array.isArray(parsed) ? parsed : [parsed];\r\n          } catch (jsonError) {\r\n            // 如果不是JSON，当作普通文件名处理\r\n            return [{ name: value, url: value }];\r\n          }\r\n        } else if (Array.isArray(value)) {\r\n          return value;\r\n        } else {\r\n          return [value];\r\n        }\r\n      } catch (e) {\r\n        // 如果解析失败，当作普通字符串处理\r\n        return [{ name: value, url: value }];\r\n      }\r\n    },\r\n\r\n    /** 获取默认字段标签 */\r\n    getDefaultFieldLabel(fieldName) {\r\n      const labelMap = {\r\n        // 项目相关\r\n        'projectName': '项目名称',\r\n        'projectDesc': '项目简介',\r\n        'projectDescription': '项目描述',\r\n        'projectType': '项目类型',\r\n        'projectStage': '项目阶段',\r\n        'projectCategory': '项目分类',\r\n\r\n        // 团队相关\r\n        'teamLeader': '团队负责人',\r\n        'teamSize': '团队规模',\r\n        'teamMember': '团队成员',\r\n        'teamDescription': '团队介绍',\r\n\r\n        // 联系信息\r\n        'name': '姓名',\r\n        'userName': '用户姓名',\r\n        'phone': '联系电话',\r\n        'userPhone': '用户手机号',\r\n        'email': '邮箱地址',\r\n        'userEmail': '用户邮箱',\r\n        'address': '联系地址',\r\n        'company': '所在公司',\r\n        'position': '职位',\r\n\r\n        // 身份信息\r\n        'idcard': '身份证号',\r\n        'studentId': '学号',\r\n        'workId': '工号',\r\n\r\n        // 时间相关\r\n        'registrationDate': '报名日期',\r\n        'registrationTime': '报名时间',\r\n        'startDate': '开始日期',\r\n        'endDate': '结束日期',\r\n        'birthDate': '出生日期',\r\n\r\n        // 文件相关\r\n        'planFile': '项目计划书',\r\n        'videoFile': '演示视频',\r\n        'resumeFile': '个人简历',\r\n        'certificateFile': '证书文件',\r\n        'attachmentFile': '附件文件',\r\n\r\n        // 选择相关\r\n        'gender': '性别',\r\n        'education': '学历',\r\n        'experience': '工作经验',\r\n        'skill': '技能',\r\n        'interest': '兴趣爱好',\r\n        'hobby': '爱好',\r\n\r\n        // 业务相关\r\n        'industry': '所属行业',\r\n        'fundingRound': '融资轮次',\r\n        'investment': '投资金额',\r\n        'revenue': '营收情况',\r\n        'website': '网站地址',\r\n        'socialMedia': '社交媒体',\r\n\r\n        // 其他\r\n        'remark': '备注',\r\n        'comment': '评论',\r\n        'feedback': '反馈',\r\n        'suggestion': '建议',\r\n        'reason': '原因',\r\n        'purpose': '目的',\r\n        'goal': '目标',\r\n        'plan': '计划',\r\n        'budget': '预算',\r\n        'requirement': '需求',\r\n        'expectation': '期望'\r\n      };\r\n\r\n      return labelMap[fieldName] || null;\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.registration-detail {\r\n  padding: 20px;\r\n}\r\n\r\n.basic-info {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.basic-info h3 {\r\n  margin: 0 0 20px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  border-bottom: 2px solid #409eff;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.info-item {\r\n  margin-bottom: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  margin-right: 10px;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-item span {\r\n  color: #303133;\r\n}\r\n\r\n.form-data {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.form-data h3 {\r\n  margin: 0 0 20px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  border-bottom: 2px solid #67c23a;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.file-display {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 12px;\r\n  background: #f5f7fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.file-link {\r\n  color: #409eff;\r\n  text-decoration: none;\r\n}\r\n\r\n.file-link:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.array-display {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 5px;\r\n}\r\n\r\n.text-value {\r\n  color: #303133;\r\n  word-break: break-all;\r\n}\r\n\r\n.empty-value {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n}\r\n\r\n.raw-data {\r\n  margin-top: 20px;\r\n}\r\n\r\n.raw-data .el-collapse {\r\n  border: none;\r\n}\r\n\r\n.raw-data .el-collapse-item__header {\r\n  background: #f5f7fa;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  padding: 0 15px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.raw-data .el-collapse-item__content {\r\n  padding: 15px 0 0 0;\r\n}\r\n</style>\r\n"]}]}