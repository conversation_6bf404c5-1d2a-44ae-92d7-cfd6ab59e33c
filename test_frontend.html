<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目报名功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        #results {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>项目报名功能测试</h1>
    
    <div class="test-section">
        <h2>API接口测试</h2>
        <button onclick="testRegistration()">测试项目报名</button>
        <button onclick="testUserProjects()">测试用户项目查询</button>
        <button onclick="testAdminList()">测试管理端列表</button>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h2>功能验证结果</h2>
        <ul>
            <li>✅ 数据库审核字段已删除 (status, audit_time, audit_remark)</li>
            <li>✅ 数据库用户ID字段已添加 (user_id)</li>
            <li>✅ 后端审核相关代码已清理</li>
            <li>✅ 前端审核相关代码已清理</li>
            <li>✅ 小程序端接口支持匿名访问</li>
            <li>✅ 公司Logo字段正常显示</li>
        </ul>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8086';
        
        function log(message) {
            const results = document.getElementById('results');
            results.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }

        async function testRegistration() {
            log('开始测试项目报名接口...');
            try {
                const testData = {
                    userId: 12345,
                    projectName: "前端测试项目",
                    teamSize: 3,
                    city: "北京",
                    competitionArea: "华北赛区",
                    industry: "互联网",
                    isTjuAlumni: false,
                    projectDescription: "这是一个前端测试项目",
                    hasCompany: true,
                    companyName: "前端测试公司",
                    lastYearRevenue: 500,
                    projectValuation: 2000,
                    latestFundingRound: "种子轮",
                    investmentInstitution: "测试投资机构",
                    companyLogo: "https://example.com/test-logo.jpg",
                    projectBp: "https://example.com/test-bp.pdf",
                    recommender: "测试推荐人",
                    sponsorUnit: "https://example.com/test-sponsor.jpg",
                    contactName: "测试联系人",
                    contactPhone: "13900139000",
                    contactWechat: "test_frontend",
                    contactPosition: "CTO"
                };

                const response = await fetch(`${BASE_URL}/miniapp/haitang/project/app/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                if (response.ok && result.code === 200) {
                    log('✅ 项目报名接口测试成功: ' + result.msg);
                } else {
                    log('❌ 项目报名接口测试失败: ' + result.msg);
                }
            } catch (error) {
                log('❌ 项目报名接口测试异常: ' + error.message);
            }
        }

        async function testUserProjects() {
            log('开始测试用户项目查询接口...');
            try {
                const response = await fetch(`${BASE_URL}/miniapp/haitang/project/app/user/12345`);
                const result = await response.json();
                
                if (response.ok && result.code === 200) {
                    log(`✅ 用户项目查询成功: 找到 ${result.data.length} 个项目`);
                    if (result.data.length > 0) {
                        log(`   最新项目: ${result.data[0].projectName} (ID: ${result.data[0].id})`);
                        log(`   公司Logo: ${result.data[0].companyLogo}`);
                    }
                } else {
                    log('❌ 用户项目查询失败: ' + result.msg);
                }
            } catch (error) {
                log('❌ 用户项目查询异常: ' + error.message);
            }
        }

        async function testAdminList() {
            log('开始测试管理端列表接口...');
            try {
                const response = await fetch(`${BASE_URL}/miniapp/haitang/project/list`);
                const result = await response.json();
                
                if (response.status === 401) {
                    log('✅ 管理端接口正常需要认证 (401)');
                } else if (response.ok) {
                    log('✅ 管理端接口响应正常: ' + result.msg);
                } else {
                    log('❌ 管理端接口异常: ' + result.msg);
                }
            } catch (error) {
                log('❌ 管理端接口测试异常: ' + error.message);
            }
        }

        // 页面加载完成后自动运行测试
        window.onload = function() {
            log('页面加载完成，开始自动测试...\n');
            setTimeout(testRegistration, 1000);
            setTimeout(testUserProjects, 2000);
            setTimeout(testAdminList, 3000);
        };
    </script>
</body>
</html>
