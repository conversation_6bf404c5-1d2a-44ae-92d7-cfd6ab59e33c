{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue", "mtime": 1754384002001}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_formConfig", "require", "_request", "_interopRequireDefault", "_auth", "name", "dicts", "data", "loading", "listLoading", "ids", "single", "multiple", "total", "formConfigList", "enabledConfig", "<PERSON><PERSON><PERSON><PERSON><PERSON>s", "title", "open", "formConfigOpen", "previewDialogVisible", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentConfig", "pickerEditData", "sponsorDialogVisible", "currentSponsorImage", "uploadAction", "process", "env", "VUE_APP_BASE_API", "uploadHeaders", "Authorization", "getToken", "queryParams", "pageNum", "pageSize", "config<PERSON><PERSON>", "status", "form", "rules", "required", "message", "trigger", "created", "loadEnabledConfig", "getList", "methods", "_this", "request", "url", "method", "then", "response", "loadEna<PERSON><PERSON><PERSON><PERSON><PERSON>s", "catch", "formConfig", "JSON", "parse", "e", "_this2", "listFormConfig", "rows", "cancel", "reset", "configId", "configDescription", "isEnabled", "sortOrder", "createBy", "createTime", "updateBy", "updateTime", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this3", "getFormConfig", "submitForm", "_this4", "$refs", "validate", "valid", "updateFormConfig", "$modal", "msgSuccess", "addFormConfig", "handleDelete", "_this5", "configIds", "confirm", "delFormConfig", "handleFormConfig", "_this6", "for<PERSON>ach", "field", "type", "options", "<PERSON><PERSON><PERSON>", "Date", "now", "getPickerOptions", "handlePreview", "handleEnable", "_this7", "enableFormConfig", "addFormField", "defaultName", "generateUniqueFieldName", "newField", "label", "push", "removeFormField", "index", "splice", "prefix", "counter", "some", "needOptions", "includes", "handleTemplateCommand", "command", "_this8", "templates", "basic", "project", "_objectSpread2", "default", "toLowerCase", "handlePreviewForm", "saveFormConfig", "_this9", "msgError", "i", "concat", "formData", "stringify", "getFieldOptions", "split", "opt", "trim", "filter", "parsed", "Array", "isArray", "text", "disabled", "addPickerOption", "updatePickerOptionsFromEdit", "removePickerOption", "getPickerOptionsForEdit", "updatePickerOptions", "_this0", "$nextTick", "pickerOptions", "handleFieldTypeChange", "defaultOptions", "join", "getOptionsPlaceholder", "placeholders", "radio", "checkbox", "select", "radio_other", "checkbox_other", "select_other", "picker", "getFieldIcon", "icons", "input", "textarea", "number", "email", "tel", "date", "file", "getFieldTypeName", "names", "handleExport", "download", "getTime", "handleSponsorUpload", "loadSponsorImage", "_this1", "getSponsorImage", "sponsorUnit", "beforeSponsorUpload", "isImage", "indexOf", "isLt5M", "size", "handleSponsorUploadSuccess", "_this10", "code", "imageUrl", "updateSponsorImage", "msg", "handleSponsorUploadError", "handleDeleteSponsor", "_this11"], "sources": ["src/views/miniapp/haitang/projectFormConfig/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 表单配置管理区域 -->\r\n    <div class=\"form-config-container\">\r\n      <div class=\"config-header\">\r\n        <h3>项目报名表单配置管理</h3>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"warning\"\r\n            icon=\"el-icon-picture\"\r\n            @click=\"handleSponsorUpload\"\r\n            v-hasPermi=\"['miniapp:haitang:project:edit']\"\r\n          >赞助商图片</el-button>\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"handleAdd\"\r\n            v-hasPermi=\"['miniapp:haitang:formConfig:add']\"\r\n          >新增配置</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"config-content\" v-loading=\"loading\">\r\n        <!-- 当前启用的配置 -->\r\n        <div v-if=\"enabledConfig\" class=\"enabled-config\">\r\n          <div class=\"enabled-header\">\r\n            <h4>\r\n              <i class=\"el-icon-check\" style=\"color: #67c23a;\"></i>\r\n              当前启用配置：{{ enabledConfig.configName }}\r\n            </h4>\r\n            <div class=\"enabled-actions\">\r\n              <el-button\r\n                size=\"small\"\r\n                type=\"primary\"\r\n                icon=\"el-icon-setting\"\r\n                @click=\"handleFormConfig(enabledConfig)\"\r\n                v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\r\n              >配置表单</el-button>\r\n              <el-button\r\n                size=\"small\"\r\n                type=\"success\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handlePreview(enabledConfig)\"\r\n              >预览表单</el-button>\r\n            </div>\r\n          </div>\r\n          <div class=\"enabled-description\">\r\n            <p>{{ enabledConfig.configDescription || '暂无描述' }}</p>\r\n          </div>\r\n          <div v-if=\"enabledFormFields.length > 0\" class=\"form-preview\">\r\n            <h5>表单字段预览：</h5>\r\n            <div class=\"field-list\">\r\n              <div v-for=\"(field, index) in enabledFormFields\" :key=\"index\" class=\"field-item\">\r\n                <div class=\"field-info\">\r\n                  <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\r\n                  <span class=\"field-label\">{{ field.label }}</span>\r\n                  <el-tag size=\"mini\" type=\"success\">{{ field.name }}</el-tag>\r\n                  <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\r\n                  <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\r\n                  <span class=\"field-type\">{{ getFieldTypeName(field.type) }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div v-else class=\"empty-form\">\r\n            <i class=\"el-icon-document-add\"></i>\r\n            <p>该配置暂未设置表单字段，点击\"配置表单\"开始设置</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 无启用配置时的提示 -->\r\n        <div v-else class=\"no-enabled-config\">\r\n          <i class=\"el-icon-warning-outline\"></i>\r\n          <p>暂无启用的表单配置，请先创建并启用一个配置</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 所有配置列表 -->\r\n    <div class=\"config-list-container\">\r\n      <div class=\"list-header\">\r\n        <h4>所有表单配置</h4>\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\">\r\n          <el-form-item label=\"配置名称\" prop=\"configName\">\r\n            <el-input\r\n              v-model=\"queryParams.configName\"\r\n              placeholder=\"请输入配置名称\"\r\n              clearable\r\n              style=\"width: 200px;\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable style=\"width: 120px;\">\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_normal_disable\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <el-table v-loading=\"listLoading\" :data=\"formConfigList\" @selection-change=\"handleSelectionChange\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column label=\"配置名称\" align=\"center\" prop=\"configName\" min-width=\"150\" />\r\n        <el-table-column label=\"配置描述\" align=\"center\" prop=\"configDescription\" show-overflow-tooltip min-width=\"200\" />\r\n        <el-table-column label=\"是否启用\" align=\"center\" prop=\"isEnabled\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.isEnabled === '1'\" type=\"success\">已启用</el-tag>\r\n            <el-tag v-else type=\"info\">未启用</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-setting\"\r\n              @click=\"handleFormConfig(scope.row)\"\r\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\r\n            >配置</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\r\n            >编辑</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-check\"\r\n              @click=\"handleEnable(scope.row)\"\r\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\r\n              v-if=\"scope.row.isEnabled !== '1'\"\r\n            >启用</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              v-hasPermi=\"['miniapp:haitang:formConfig:remove']\"\r\n            >删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 添加或修改配置基本信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-form-item label=\"配置名称\" prop=\"configName\">\r\n          <el-input v-model=\"form.configName\" placeholder=\"请输入配置名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"配置描述\" prop=\"configDescription\">\r\n          <el-input v-model=\"form.configDescription\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入配置描述\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"sortOrder\">\r\n          <el-input-number v-model=\"form.sortOrder\" controls-position=\"right\" :min=\"0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"status\">\r\n          <el-radio-group v-model=\"form.status\">\r\n            <el-radio\r\n              v-for=\"dict in dict.type.sys_normal_disable\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.value\"\r\n            >{{dict.label}}</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单配置对话框 -->\r\n    <el-dialog title=\"表单字段配置\" :visible.sync=\"formConfigOpen\" width=\"1000px\" append-to-body>\r\n      <div class=\"form-fields-config\">\r\n        <!-- 工具栏 -->\r\n        <div class=\"form-fields-toolbar\">\r\n          <div class=\"toolbar-left\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">\r\n              添加字段\r\n            </el-button>\r\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"small\">\r\n              <el-button size=\"small\">\r\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n              </el-button>\r\n              <el-dropdown-menu slot=\"dropdown\">\r\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\r\n                <el-dropdown-item command=\"project\">项目报名模板</el-dropdown-item>\r\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </el-dropdown>\r\n          </div>\r\n          <div class=\"toolbar-right\">\r\n            <el-button size=\"small\" @click=\"handlePreviewForm\" icon=\"el-icon-view\">预览表单</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 字段配置列表 -->\r\n        <div class=\"form-fields-list\">\r\n          <div v-if=\"currentFormFields.length === 0\" class=\"empty-fields\">\r\n            <i class=\"el-icon-document-add\"></i>\r\n            <p>暂无字段，点击\"添加字段\"开始配置</p>\r\n          </div>\r\n          <div v-else>\r\n            <div v-for=\"(field, index) in currentFormFields\" :key=\"index\" class=\"field-config-item\">\r\n              <div class=\"field-config-header\">\r\n                <span class=\"field-index\">{{ index + 1 }}</span>\r\n                <el-input v-model=\"field.label\" placeholder=\"字段标签\" size=\"small\" style=\"width: 150px;\" />\r\n                <el-input v-model=\"field.name\" placeholder=\"字段名称\" size=\"small\" style=\"width: 120px;\" />\r\n                <el-select v-model=\"field.type\" placeholder=\"字段类型\" size=\"small\" style=\"width: 140px;\" @change=\"handleFieldTypeChange(field)\">\r\n                  <el-option label=\"📝 文本输入\" value=\"input\" />\r\n                  <el-option label=\"📄 多行文本\" value=\"textarea\" />\r\n                  <el-option label=\"🔢 数字输入\" value=\"number\" />\r\n                  <el-option label=\"📧 邮箱\" value=\"email\" />\r\n                  <el-option label=\"📞 电话\" value=\"tel\" />\r\n                  <el-option label=\"🔘 单选\" value=\"radio\" />\r\n                  <el-option label=\"☑️ 多选\" value=\"checkbox\" />\r\n                  <el-option label=\"📋 下拉选择\" value=\"select\" />\r\n                  <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\r\n                  <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\r\n                  <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\r\n                  <el-option label=\"🎡 滚动单选\" value=\"picker\" />\r\n                  <el-option label=\"📅 日期\" value=\"date\" />\r\n                  <el-option label=\"📎 文件上传\" value=\"file\" />\r\n                </el-select>\r\n                <el-checkbox v-model=\"field.required\" size=\"small\">必填</el-checkbox>\r\n                <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"removeFormField(index)\">删除</el-button>\r\n              </div>\r\n              <div class=\"field-config-body\" v-if=\"needOptions(field.type)\">\r\n                <!-- 滚动单选特殊配置 -->\r\n                <div v-if=\"field.type === 'picker'\" class=\"picker-options-config\">\r\n                  <div class=\"picker-options-header\">\r\n                    <span>选项配置（支持禁用选项）</span>\r\n                    <el-button size=\"mini\" type=\"primary\" @click=\"addPickerOption(field)\">添加选项</el-button>\r\n                  </div>\r\n                  <div class=\"picker-options-list\">\r\n                    <div v-for=\"(option, optIndex) in getPickerOptionsForEdit(field)\" :key=\"optIndex\" class=\"picker-option-item\">\r\n                      <el-input v-model=\"option.text\" placeholder=\"选项内容\" size=\"small\" style=\"width: 200px;\" @input=\"updatePickerOptionsFromEdit(field)\" />\r\n                      <el-checkbox v-model=\"option.disabled\" size=\"small\" @change=\"updatePickerOptionsFromEdit(field)\">禁用</el-checkbox>\r\n                      <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"removePickerOption(field, optIndex)\">删除</el-button>\r\n                    </div>\r\n                  </div>\r\n                  <div v-if=\"getPickerOptionsForEdit(field).length === 0\" class=\"empty-picker-options\">\r\n                    <span>暂无选项，点击\"添加选项\"开始配置</span>\r\n                  </div>\r\n                </div>\r\n                <!-- 其他字段类型的普通配置 -->\r\n                <el-input\r\n                  v-else\r\n                  v-model=\"field.options\"\r\n                  :placeholder=\"getOptionsPlaceholder(field.type)\"\r\n                  size=\"small\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"saveFormConfig\">保存配置</el-button>\r\n        <el-button @click=\"formConfigOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单预览对话框 -->\r\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\r\n      <div class=\"form-preview\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ currentConfig ? currentConfig.configName : '项目报名表' }}</h3>\r\n          <p>请填写以下信息完成报名</p>\r\n        </div>\r\n        <div class=\"preview-form\">\r\n          <div v-for=\"(field, index) in currentFormFields\" :key=\"index\" class=\"preview-field\">\r\n            <label class=\"preview-label\">\r\n              {{ field.label }}\r\n              <span v-if=\"field.required\" class=\"required\">*</span>\r\n            </label>\r\n            <div class=\"preview-input\">\r\n              <!-- 基础输入类型 -->\r\n              <el-input\r\n                v-if=\"['input', 'email', 'tel'].includes(field.type)\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <el-input\r\n                v-else-if=\"field.type === 'textarea'\"\r\n                type=\"textarea\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <!-- 数字类型 -->\r\n              <el-input-number\r\n                v-else-if=\"field.type === 'number'\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <!-- 单选类型 -->\r\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\r\n                <el-radio\r\n                  v-for=\"option in getFieldOptions(field.options)\"\r\n                  :key=\"option\"\r\n                  :label=\"option\"\r\n                >{{ option }}</el-radio>\r\n              </el-radio-group>\r\n              <!-- 单选+其他 -->\r\n              <div v-else-if=\"field.type === 'radio_other'\">\r\n                <el-radio-group disabled style=\"margin-bottom: 8px;\">\r\n                  <el-radio\r\n                    v-for=\"option in getFieldOptions(field.options)\"\r\n                    :key=\"option\"\r\n                    :label=\"option\"\r\n                  >{{ option }}</el-radio>\r\n                  <el-radio label=\"其他\">其他</el-radio>\r\n                </el-radio-group>\r\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\r\n              </div>\r\n              <!-- 多选类型 -->\r\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\r\n                <el-checkbox\r\n                  v-for=\"option in getFieldOptions(field.options)\"\r\n                  :key=\"option\"\r\n                  :label=\"option\"\r\n                >{{ option }}</el-checkbox>\r\n              </el-checkbox-group>\r\n              <!-- 多选+其他 -->\r\n              <div v-else-if=\"field.type === 'checkbox_other'\">\r\n                <el-checkbox-group disabled style=\"margin-bottom: 8px;\">\r\n                  <el-checkbox\r\n                    v-for=\"option in getFieldOptions(field.options)\"\r\n                    :key=\"option\"\r\n                    :label=\"option\"\r\n                  >{{ option }}</el-checkbox>\r\n                  <el-checkbox label=\"其他\">其他</el-checkbox>\r\n                </el-checkbox-group>\r\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\r\n              </div>\r\n              <!-- 下拉选择 -->\r\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled>\r\n                <el-option\r\n                  v-for=\"option in getFieldOptions(field.options)\"\r\n                  :key=\"option\"\r\n                  :label=\"option\"\r\n                  :value=\"option\"\r\n                />\r\n              </el-select>\r\n              <!-- 下拉+其他 -->\r\n              <div v-else-if=\"field.type === 'select_other'\">\r\n                <el-select :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"margin-bottom: 8px; width: 100%;\">\r\n                  <el-option\r\n                    v-for=\"option in getFieldOptions(field.options)\"\r\n                    :key=\"option\"\r\n                    :label=\"option\"\r\n                    :value=\"option\"\r\n                  />\r\n                  <el-option label=\"其他\" value=\"其他\" />\r\n                </el-select>\r\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\r\n              </div>\r\n              <!-- 滚动单选 -->\r\n              <div v-else-if=\"field.type === 'picker'\" class=\"picker-preview\">\r\n                <div class=\"picker-display\">\r\n                  <span class=\"picker-placeholder\">{{ '请选择' + field.label }}</span>\r\n                  <i class=\"el-icon-arrow-down\"></i>\r\n                </div>\r\n                <div class=\"picker-options-preview\">\r\n                  <div v-for=\"(option, index) in getPickerOptions(field.options)\" :key=\"index\"\r\n                       :class=\"['picker-option-preview', { 'disabled': option.disabled }]\">\r\n                    {{ option.text }}\r\n                    <span v-if=\"option.disabled\" class=\"disabled-tag\">禁用</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- 日期类型 -->\r\n              <el-date-picker\r\n                v-else-if=\"field.type === 'date'\"\r\n                type=\"date\"\r\n                :placeholder=\"'请选择' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <!-- 文件上传 -->\r\n              <el-upload\r\n                v-else-if=\"field.type === 'file'\"\r\n                class=\"upload-demo\"\r\n                action=\"#\"\r\n                :auto-upload=\"false\"\r\n                disabled\r\n              >\r\n                <el-button size=\"small\" type=\"primary\" disabled>点击上传</el-button>\r\n              </el-upload>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 赞助商图片上传对话框 -->\r\n    <el-dialog title=\"赞助商图片管理\" :visible.sync=\"sponsorDialogVisible\" width=\"600px\" append-to-body>\r\n      <div class=\"sponsor-upload-container\">\r\n        <div class=\"current-sponsor\" v-if=\"currentSponsorImage\">\r\n          <h4>当前赞助商图片</h4>\r\n          <div class=\"sponsor-image-preview\">\r\n            <img :src=\"currentSponsorImage\" alt=\"赞助商图片\" class=\"sponsor-img\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"upload-section\">\r\n          <h4>{{ currentSponsorImage ? '更换赞助商图片' : '上传赞助商图片' }}</h4>\r\n          <el-upload\r\n            class=\"sponsor-uploader\"\r\n            :action=\"uploadAction\"\r\n            :headers=\"uploadHeaders\"\r\n            :show-file-list=\"false\"\r\n            :on-success=\"handleSponsorUploadSuccess\"\r\n            :on-error=\"handleSponsorUploadError\"\r\n            :before-upload=\"beforeSponsorUpload\"\r\n            accept=\"image/*\"\r\n          >\r\n            <div class=\"upload-area\">\r\n              <i class=\"el-icon-plus sponsor-uploader-icon\"></i>\r\n              <div class=\"upload-text\">点击上传图片</div>\r\n              <div class=\"upload-tip\">支持 JPG、PNG 格式，建议尺寸 400x200px</div>\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n\r\n        <div class=\"sponsor-actions\" v-if=\"currentSponsorImage\">\r\n          <el-button type=\"danger\" @click=\"handleDeleteSponsor\">删除当前图片</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"sponsorDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listFormConfig, getFormConfig, delFormConfig, addFormConfig, updateFormConfig, enableFormConfig, getSponsorImage, updateSponsorImage } from \"@/api/miniapp/haitang/formConfig\";\r\nimport request from '@/utils/request';\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"FormConfig\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      listLoading: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 天大海棠杯项目报名表单配置表格数据\r\n      formConfigList: [],\r\n      // 当前启用的配置\r\n      enabledConfig: null,\r\n      // 启用配置的表单字段\r\n      enabledFormFields: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示表单配置弹出层\r\n      formConfigOpen: false,\r\n      // 是否显示表单预览弹出层\r\n      previewDialogVisible: false,\r\n      // 当前配置的表单字段\r\n      currentFormFields: [],\r\n      // 当前操作的配置\r\n      currentConfig: null,\r\n      // 滚动单选字段的编辑数据缓存\r\n      pickerEditData: {},\r\n      // 赞助商图片相关\r\n      sponsorDialogVisible: false,\r\n      currentSponsorImage: '',\r\n      uploadAction: process.env.VUE_APP_BASE_API + \"/common/upload\",\r\n      uploadHeaders: { Authorization: \"Bearer \" + getToken() },\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        configName: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        configName: [\r\n          { required: true, message: \"配置名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.loadEnabledConfig();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 加载启用的配置 */\r\n    loadEnabledConfig() {\r\n      this.loading = true;\r\n      request({\r\n        url: '/miniapp/haitang/formConfig/enabled',\r\n        method: 'get'\r\n      }).then(response => {\r\n        if (response.data) {\r\n          this.enabledConfig = response.data;\r\n          this.loadEnabledFormFields();\r\n        } else {\r\n          this.enabledConfig = null;\r\n          this.enabledFormFields = [];\r\n        }\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.enabledConfig = null;\r\n        this.enabledFormFields = [];\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 加载启用配置的表单字段 */\r\n    loadEnabledFormFields() {\r\n      if (this.enabledConfig && this.enabledConfig.formConfig) {\r\n        try {\r\n          this.enabledFormFields = JSON.parse(this.enabledConfig.formConfig);\r\n        } catch (e) {\r\n          this.enabledFormFields = [];\r\n        }\r\n      } else {\r\n        this.enabledFormFields = [];\r\n      }\r\n    },\r\n    /** 查询天大海棠杯项目报名表单配置列表 */\r\n    getList() {\r\n      this.listLoading = true;\r\n      listFormConfig(this.queryParams).then(response => {\r\n        this.formConfigList = response.rows;\r\n        this.total = response.total;\r\n        this.listLoading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        configId: null,\r\n        configName: null,\r\n        configDescription: null,\r\n        formConfig: null,\r\n        isEnabled: \"0\",\r\n        sortOrder: 0,\r\n        status: \"0\",\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.configId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加天大海棠杯项目报名表单配置\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const configId = row.configId || this.ids\r\n      getFormConfig(configId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改天大海棠杯项目报名表单配置\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.configId != null) {\r\n            updateFormConfig(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addFormConfig(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const configIds = row.configId || this.ids;\r\n      this.$modal.confirm('是否确认删除天大海棠杯项目报名表单配置编号为\"' + configIds + '\"的数据项？').then(function() {\r\n        return delFormConfig(configIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 表单配置按钮操作 */\r\n    handleFormConfig(row) {\r\n      this.currentConfig = row;\r\n      this.currentFormFields = [];\r\n      this.pickerEditData = {}; // 清空编辑数据缓存\r\n\r\n      if (row.formConfig) {\r\n        try {\r\n          this.currentFormFields = JSON.parse(row.formConfig);\r\n          // 初始化滚动单选字段的编辑数据\r\n          this.currentFormFields.forEach(field => {\r\n            if (field.type === 'picker' && field.options) {\r\n              const fieldKey = field.name || 'temp_' + Date.now();\r\n              this.pickerEditData[fieldKey] = this.getPickerOptions(field.options);\r\n            }\r\n          });\r\n        } catch (e) {\r\n          this.currentFormFields = [];\r\n        }\r\n      }\r\n      this.formConfigOpen = true;\r\n    },\r\n    /** 预览按钮操作 */\r\n    handlePreview(row) {\r\n      this.currentConfig = row;\r\n      this.currentFormFields = [];\r\n      if (row.formConfig) {\r\n        try {\r\n          this.currentFormFields = JSON.parse(row.formConfig);\r\n        } catch (e) {\r\n          this.currentFormFields = [];\r\n        }\r\n      }\r\n      this.previewDialogVisible = true;\r\n    },\r\n    /** 启用按钮操作 */\r\n    handleEnable(row) {\r\n      this.$modal.confirm('启用此配置将禁用其他所有配置，是否确认启用？').then(function() {\r\n        return enableFormConfig(row.configId);\r\n      }).then(() => {\r\n        this.loadEnabledConfig();\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"启用成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 添加表单字段 */\r\n    addFormField() {\r\n      const defaultName = this.generateUniqueFieldName('field');\r\n      const newField = {\r\n        name: defaultName,\r\n        label: '',\r\n        type: 'input',\r\n        required: false,\r\n        options: ''\r\n      };\r\n      this.currentFormFields.push(newField);\r\n    },\r\n    /** 删除表单字段 */\r\n    removeFormField(index) {\r\n      this.currentFormFields.splice(index, 1);\r\n    },\r\n    /** 生成唯一字段名 */\r\n    generateUniqueFieldName(prefix) {\r\n      let counter = 1;\r\n      let name = prefix + counter;\r\n      while (this.currentFormFields.some(field => field.name === name)) {\r\n        counter++;\r\n        name = prefix + counter;\r\n      }\r\n      return name;\r\n    },\r\n    /** 判断字段类型是否需要选项 */\r\n    needOptions(type) {\r\n      return ['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other', 'picker'].includes(type);\r\n    },\r\n    /** 处理模板命令 */\r\n    handleTemplateCommand(command) {\r\n      if (command === 'clear') {\r\n        this.$modal.confirm('确认清空所有字段？').then(() => {\r\n          this.currentFormFields = [];\r\n        });\r\n        return;\r\n      }\r\n\r\n      const templates = {\r\n        basic: [\r\n          { label: '姓名', name: '', type: 'input', required: true, options: '' },\r\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\r\n          { label: '邮箱地址', name: '', type: 'email', required: false, options: '' }\r\n        ],\r\n        project: [\r\n          { label: '项目名称', name: '', type: 'input', required: true, options: '' },\r\n          { label: '团队负责人', name: '', type: 'input', required: true, options: '' },\r\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\r\n          { label: '邮箱地址', name: '', type: 'email', required: true, options: '' },\r\n          { label: '项目简介', name: '', type: 'textarea', required: true, options: '' },\r\n          { label: '项目类型', name: '', type: 'select_other', required: true, options: '科技创新,商业模式,社会公益,文化创意' },\r\n          { label: '团队规模', name: '', type: 'radio', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\r\n          { label: '项目阶段', name: '', type: 'picker', required: true, options: '[{\"text\":\"创意阶段\",\"disabled\":false},{\"text\":\"初创阶段\",\"disabled\":false},{\"text\":\"成长阶段\",\"disabled\":false},{\"text\":\"成熟阶段\",\"disabled\":true}]' },\r\n          { label: '报名日期', name: '', type: 'date', required: false, options: '' },\r\n          { label: '项目计划书', name: '', type: 'file', required: true, options: '' },\r\n          { label: '演示视频', name: '', type: 'file', required: false, options: '' }\r\n        ]\r\n      };\r\n\r\n      if (templates[command]) {\r\n        this.currentFormFields = templates[command].map(field => ({\r\n          ...field,\r\n          name: this.generateUniqueFieldName(field.label.toLowerCase())\r\n        }));\r\n      }\r\n    },\r\n    /** 预览表单 */\r\n    handlePreviewForm() {\r\n      this.previewDialogVisible = true;\r\n    },\r\n    /** 保存表单配置 */\r\n    saveFormConfig() {\r\n      if (!this.currentConfig) {\r\n        this.$modal.msgError(\"请先选择要配置的表单\");\r\n        return;\r\n      }\r\n\r\n      // 验证字段配置\r\n      for (let i = 0; i < this.currentFormFields.length; i++) {\r\n        const field = this.currentFormFields[i];\r\n        if (!field.label) {\r\n          this.$modal.msgError(`第${i + 1}个字段的标签不能为空`);\r\n          return;\r\n        }\r\n        if (!field.name) {\r\n          field.name = this.generateUniqueFieldName(field.label.toLowerCase());\r\n        }\r\n        if (this.needOptions(field.type) && !field.options) {\r\n          this.$modal.msgError(`第${i + 1}个字段\"${field.label}\"需要设置选项内容`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      const formData = {\r\n        configId: this.currentConfig.configId,\r\n        formConfig: JSON.stringify(this.currentFormFields)\r\n      };\r\n\r\n      updateFormConfig(formData).then(response => {\r\n        this.$modal.msgSuccess(\"表单配置保存成功\");\r\n        this.formConfigOpen = false;\r\n        this.loadEnabledConfig();\r\n        this.getList();\r\n      });\r\n    },\r\n    /** 获取字段选项 */\r\n    getFieldOptions(options) {\r\n      if (!options) return [];\r\n      return options.split(',').map(opt => opt.trim()).filter(opt => opt);\r\n    },\r\n    /** 获取滚动单选选项 */\r\n    getPickerOptions(options) {\r\n      if (!options) return [];\r\n      try {\r\n        const parsed = JSON.parse(options);\r\n        // 确保返回的是数组格式\r\n        return Array.isArray(parsed) ? parsed : [];\r\n      } catch (e) {\r\n        // 如果解析失败，尝试按逗号分隔的格式处理\r\n        if (typeof options === 'string' && options.trim()) {\r\n          return options.split(',').map(opt => ({\r\n            text: opt.trim(),\r\n            disabled: false\r\n          })).filter(opt => opt.text);\r\n        }\r\n        return [];\r\n      }\r\n    },\r\n    /** 添加滚动单选选项 */\r\n    addPickerOption(field) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n      if (!this.pickerEditData[fieldKey]) {\r\n        this.pickerEditData[fieldKey] = this.getPickerOptions(field.options);\r\n      }\r\n      this.pickerEditData[fieldKey].push({\r\n        text: '',\r\n        disabled: false\r\n      });\r\n      this.updatePickerOptionsFromEdit(field);\r\n    },\r\n    /** 删除滚动单选选项 */\r\n    removePickerOption(field, index) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n      if (this.pickerEditData[fieldKey]) {\r\n        this.pickerEditData[fieldKey].splice(index, 1);\r\n        this.updatePickerOptionsFromEdit(field);\r\n      }\r\n    },\r\n    /** 获取用于编辑的滚动单选选项 */\r\n    getPickerOptionsForEdit(field) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n      if (!this.pickerEditData[fieldKey]) {\r\n        // 初始化编辑数据\r\n        this.pickerEditData[fieldKey] = this.getPickerOptions(field.options);\r\n      }\r\n      return this.pickerEditData[fieldKey];\r\n    },\r\n    /** 从编辑数据更新滚动单选选项 */\r\n    updatePickerOptionsFromEdit(field) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n      if (this.pickerEditData[fieldKey]) {\r\n        field.options = JSON.stringify(this.pickerEditData[fieldKey]);\r\n      }\r\n    },\r\n    /** 更新滚动单选选项 */\r\n    updatePickerOptions(field) {\r\n      // 延迟更新，确保数据已经变更\r\n      this.$nextTick(() => {\r\n        let pickerOptions = this.getPickerOptions(field.options);\r\n        field.options = JSON.stringify(pickerOptions);\r\n      });\r\n    },\r\n    /** 处理字段类型变更 */\r\n    handleFieldTypeChange(field) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n\r\n      // 当切换到滚动单选时，如果没有选项则初始化\r\n      if (field.type === 'picker') {\r\n        if (!field.options || field.options === '') {\r\n          const defaultOptions = [\r\n            { text: '选项1', disabled: false },\r\n            { text: '选项2', disabled: false }\r\n          ];\r\n          field.options = JSON.stringify(defaultOptions);\r\n          this.pickerEditData[fieldKey] = defaultOptions;\r\n        } else {\r\n          // 重新初始化编辑数据\r\n          this.pickerEditData[fieldKey] = this.getPickerOptions(field.options);\r\n        }\r\n      }\r\n      // 当切换到其他需要选项的字段类型时，如果是JSON格式则转换为逗号分隔\r\n      else if (this.needOptions(field.type) && field.type !== 'picker' && field.options) {\r\n        // 清除编辑数据缓存\r\n        delete this.pickerEditData[fieldKey];\r\n\r\n        try {\r\n          const parsed = JSON.parse(field.options);\r\n          if (Array.isArray(parsed)) {\r\n            field.options = parsed.map(opt => opt.text || opt.label || opt).join(',');\r\n          }\r\n        } catch (e) {\r\n          // 如果不是JSON格式，保持原样\r\n        }\r\n      } else {\r\n        // 清除编辑数据缓存\r\n        delete this.pickerEditData[fieldKey];\r\n      }\r\n    },\r\n    /** 获取选项输入框占位符 */\r\n    getOptionsPlaceholder(type) {\r\n      const placeholders = {\r\n        radio: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\r\n        checkbox: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\r\n        select: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\r\n        radio_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\r\n        checkbox_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\r\n        select_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\r\n        picker: '滚动单选选项，支持禁用功能，请使用上方的选项配置器进行设置'\r\n      };\r\n      return placeholders[type] || '选项内容，多个选项用逗号分隔';\r\n    },\r\n    /** 获取字段图标 */\r\n    getFieldIcon(type) {\r\n      const icons = {\r\n        input: 'el-icon-edit',\r\n        textarea: 'el-icon-document',\r\n        number: 'el-icon-s-data',\r\n        email: 'el-icon-message',\r\n        tel: 'el-icon-phone',\r\n        radio: 'el-icon-circle-check',\r\n        checkbox: 'el-icon-check',\r\n        select: 'el-icon-arrow-down',\r\n        radio_other: 'el-icon-circle-plus-outline',\r\n        checkbox_other: 'el-icon-circle-plus-outline',\r\n        select_other: 'el-icon-circle-plus-outline',\r\n        picker: 'el-icon-sort',\r\n        date: 'el-icon-date',\r\n        file: 'el-icon-upload'\r\n      };\r\n      return icons[type] || 'el-icon-edit';\r\n    },\r\n    /** 获取字段类型名称 */\r\n    getFieldTypeName(type) {\r\n      const names = {\r\n        input: '文本输入',\r\n        textarea: '多行文本',\r\n        number: '数字输入',\r\n        email: '邮箱',\r\n        tel: '电话',\r\n        radio: '单选',\r\n        checkbox: '多选',\r\n        select: '下拉选择',\r\n        radio_other: '单选+其他',\r\n        checkbox_other: '多选+其他',\r\n        select_other: '下拉+其他',\r\n        picker: '滚动单选',\r\n        date: '日期',\r\n        file: '文件上传'\r\n      };\r\n      return names[type] || '未知类型';\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/haitang/formConfig/export', {\r\n        ...this.queryParams\r\n      }, `formConfig_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 赞助商图片上传按钮操作 */\r\n    handleSponsorUpload() {\r\n      this.loadSponsorImage();\r\n      this.sponsorDialogVisible = true;\r\n    },\r\n    /** 加载赞助商图片 */\r\n    loadSponsorImage() {\r\n      getSponsorImage().then(response => {\r\n        if (response.data && response.data.sponsorUnit) {\r\n          this.currentSponsorImage = response.data.sponsorUnit;\r\n        } else {\r\n          this.currentSponsorImage = '';\r\n        }\r\n      }).catch(() => {\r\n        this.currentSponsorImage = '';\r\n      });\r\n    },\r\n    /** 上传前校验 */\r\n    beforeSponsorUpload(file) {\r\n      const isImage = file.type.indexOf('image/') === 0;\r\n      const isLt5M = file.size / 1024 / 1024 < 5;\r\n\r\n      if (!isImage) {\r\n        this.$modal.msgError('上传文件只能是图片格式!');\r\n        return false;\r\n      }\r\n      if (!isLt5M) {\r\n        this.$modal.msgError('上传图片大小不能超过 5MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    /** 上传成功回调 */\r\n    handleSponsorUploadSuccess(response) {\r\n      if (response.code === 200) {\r\n        const imageUrl = response.url;\r\n        updateSponsorImage(imageUrl).then(() => {\r\n          this.currentSponsorImage = imageUrl;\r\n          this.$modal.msgSuccess('赞助商图片上传成功');\r\n        }).catch(() => {\r\n          this.$modal.msgError('保存赞助商图片失败');\r\n        });\r\n      } else {\r\n        this.$modal.msgError('图片上传失败：' + response.msg);\r\n      }\r\n    },\r\n    /** 上传失败回调 */\r\n    handleSponsorUploadError() {\r\n      this.$modal.msgError('图片上传失败，请重试');\r\n    },\r\n    /** 删除赞助商图片 */\r\n    handleDeleteSponsor() {\r\n      this.$modal.confirm('确认删除当前赞助商图片？').then(() => {\r\n        updateSponsorImage('').then(() => {\r\n          this.currentSponsorImage = '';\r\n          this.$modal.msgSuccess('赞助商图片删除成功');\r\n        }).catch(() => {\r\n          this.$modal.msgError('删除赞助商图片失败');\r\n        });\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-config-container {\r\n  margin-bottom: 20px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.config-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.config-header h3 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.config-content {\r\n  padding: 24px;\r\n  min-height: 200px;\r\n}\r\n\r\n.enabled-config {\r\n  border: 1px solid #67c23a;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  background: #f0f9ff;\r\n}\r\n\r\n.enabled-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.enabled-header h4 {\r\n  margin: 0;\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.enabled-description {\r\n  margin-bottom: 16px;\r\n  color: #606266;\r\n}\r\n\r\n.form-preview h5 {\r\n  margin: 0 0 12px 0;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.field-list {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-item {\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  background: #fafbfc;\r\n}\r\n\r\n.field-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.field-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.field-icon {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.field-label {\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.field-type {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.empty-form, .no-enabled-config {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-form i, .no-enabled-config i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.config-list-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.list-header {\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.list-header h4 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.form-fields-config {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-fields-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.form-fields-list {\r\n  padding: 16px;\r\n}\r\n\r\n.empty-fields {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-fields i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.field-config-item {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  margin-bottom: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-config-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 16px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.field-index {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: #409eff;\r\n  color: white;\r\n  border-radius: 50%;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.field-config-body {\r\n  padding: 12px 16px;\r\n}\r\n\r\n.preview-header {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n}\r\n\r\n.preview-header p {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.preview-field {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.required {\r\n  color: #f56c6c;\r\n  margin-left: 4px;\r\n}\r\n\r\n.preview-input {\r\n  width: 100%;\r\n}\r\n\r\n/* 滚动单选配置样式 */\r\n.picker-options-config {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  padding: 12px;\r\n  background: #fafbfc;\r\n}\r\n\r\n.picker-options-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.picker-options-list {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.picker-option-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 8px;\r\n  padding: 8px;\r\n  background: #fff;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n}\r\n\r\n.picker-option-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.empty-picker-options {\r\n  text-align: center;\r\n  padding: 20px;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 滚动单选预览样式 */\r\n.picker-preview {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.picker-display {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  background: #fff;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.picker-placeholder {\r\n  color: #c0c4cc;\r\n  font-size: 14px;\r\n}\r\n\r\n.picker-options-preview {\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.picker-option-preview {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.picker-option-preview:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.picker-option-preview.disabled {\r\n  color: #c0c4cc;\r\n  background: #f5f7fa;\r\n}\r\n\r\n.disabled-tag {\r\n  font-size: 12px;\r\n  color: #f56c6c;\r\n  background: #fef0f0;\r\n  padding: 2px 6px;\r\n  border-radius: 2px;\r\n}\r\n</style>\r\n\r\n<style scoped>\r\n.form-config-container {\r\n  margin-bottom: 20px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.config-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.config-header h3 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.config-content {\r\n  padding: 24px;\r\n  min-height: 200px;\r\n}\r\n\r\n.enabled-config {\r\n  border: 1px solid #67c23a;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  background: #f0f9ff;\r\n}\r\n\r\n.enabled-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.enabled-header h4 {\r\n  margin: 0;\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.enabled-description {\r\n  margin-bottom: 16px;\r\n  color: #606266;\r\n}\r\n\r\n.form-preview h5 {\r\n  margin: 0 0 12px 0;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.field-list {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-item {\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  background: #fafbfc;\r\n}\r\n\r\n.field-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.field-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.field-icon {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.field-label {\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.field-type {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.empty-form, .no-enabled-config {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-form i, .no-enabled-config i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.config-list-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.list-header {\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.list-header h4 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.form-fields-config {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-fields-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.form-fields-list {\r\n  padding: 16px;\r\n}\r\n\r\n.empty-fields {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-fields i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.field-config-item {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  margin-bottom: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-config-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 16px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.field-index {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: #409eff;\r\n  color: white;\r\n  border-radius: 50%;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.field-config-body {\r\n  padding: 12px 16px;\r\n}\r\n\r\n.preview-header {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n}\r\n\r\n.preview-header p {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.preview-field {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.required {\r\n  color: #f56c6c;\r\n  margin-left: 4px;\r\n}\r\n\r\n.preview-input {\r\n  width: 100%;\r\n}\r\n\r\n/* 赞助商图片上传样式 */\r\n.sponsor-upload-container {\r\n  padding: 20px;\r\n}\r\n\r\n.current-sponsor {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.current-sponsor h4 {\r\n  margin: 0 0 15px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.sponsor-image-preview {\r\n  text-align: center;\r\n  padding: 20px;\r\n  border: 2px dashed #e4e7ed;\r\n  border-radius: 6px;\r\n  background: #fafbfc;\r\n}\r\n\r\n.sponsor-img {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.upload-section h4 {\r\n  margin: 0 0 15px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.sponsor-uploader {\r\n  width: 100%;\r\n}\r\n\r\n.upload-area {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #fafbfc;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-area:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.sponsor-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.upload-text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.upload-tip {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.sponsor-actions {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e4e7ed;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA6dA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACAC,WAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,aAAA;MACA;MACAC,iBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,cAAA;MACA;MACAC,oBAAA;MACA;MACAC,iBAAA;MACA;MACAC,aAAA;MACA;MACAC,cAAA;MACA;MACAC,oBAAA;MACAC,mBAAA;MACAC,YAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACAC,aAAA;QAAAC,aAAA,kBAAAC,cAAA;MAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAH,UAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,cACAF,iBAAA,WAAAA,kBAAA;MAAA,IAAAG,KAAA;MACA,KAAAvC,OAAA;MACA,IAAAwC,gBAAA;QACAC,GAAA;QACAC,MAAA;MACA,GAAAC,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAA7C,IAAA;UACAwC,KAAA,CAAAhC,aAAA,GAAAqC,QAAA,CAAA7C,IAAA;UACAwC,KAAA,CAAAM,qBAAA;QACA;UACAN,KAAA,CAAAhC,aAAA;UACAgC,KAAA,CAAA/B,iBAAA;QACA;QACA+B,KAAA,CAAAvC,OAAA;MACA,GAAA8C,KAAA;QACAP,KAAA,CAAAhC,aAAA;QACAgC,KAAA,CAAA/B,iBAAA;QACA+B,KAAA,CAAAvC,OAAA;MACA;IACA;IACA,kBACA6C,qBAAA,WAAAA,sBAAA;MACA,SAAAtC,aAAA,SAAAA,aAAA,CAAAwC,UAAA;QACA;UACA,KAAAvC,iBAAA,GAAAwC,IAAA,CAAAC,KAAA,MAAA1C,aAAA,CAAAwC,UAAA;QACA,SAAAG,CAAA;UACA,KAAA1C,iBAAA;QACA;MACA;QACA,KAAAA,iBAAA;MACA;IACA;IACA,wBACA6B,OAAA,WAAAA,QAAA;MAAA,IAAAc,MAAA;MACA,KAAAlD,WAAA;MACA,IAAAmD,0BAAA,OAAA3B,WAAA,EAAAkB,IAAA,WAAAC,QAAA;QACAO,MAAA,CAAA7C,cAAA,GAAAsC,QAAA,CAAAS,IAAA;QACAF,MAAA,CAAA9C,KAAA,GAAAuC,QAAA,CAAAvC,KAAA;QACA8C,MAAA,CAAAlD,WAAA;MACA;IACA;IACA;IACAqD,MAAA,WAAAA,OAAA;MACA,KAAA5C,IAAA;MACA,KAAA6C,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAzB,IAAA;QACA0B,QAAA;QACA5B,UAAA;QACA6B,iBAAA;QACAV,UAAA;QACAW,SAAA;QACAC,SAAA;QACA9B,MAAA;QACA+B,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAzC,WAAA,CAAAC,OAAA;MACA,KAAAW,OAAA;IACA;IACA,aACA8B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAnE,GAAA,GAAAmE,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAf,QAAA;MAAA;MACA,KAAArD,MAAA,GAAAkE,SAAA,CAAAG,MAAA;MACA,KAAApE,QAAA,IAAAiE,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAlB,KAAA;MACA,KAAA7C,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAiE,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAArB,KAAA;MACA,IAAAC,QAAA,GAAAmB,GAAA,CAAAnB,QAAA,SAAAtD,GAAA;MACA,IAAA2E,yBAAA,EAAArB,QAAA,EAAAb,IAAA,WAAAC,QAAA;QACAgC,MAAA,CAAA9C,IAAA,GAAAc,QAAA,CAAA7C,IAAA;QACA6E,MAAA,CAAAlE,IAAA;QACAkE,MAAA,CAAAnE,KAAA;MACA;IACA;IACA,WACAqE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAjD,IAAA,CAAA0B,QAAA;YACA,IAAA2B,4BAAA,EAAAJ,MAAA,CAAAjD,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAArE,IAAA;cACAqE,MAAA,CAAA1C,OAAA;YACA;UACA;YACA,IAAAiD,yBAAA,EAAAP,MAAA,CAAAjD,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAArE,IAAA;cACAqE,MAAA,CAAA1C,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAkD,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,SAAA,GAAAd,GAAA,CAAAnB,QAAA,SAAAtD,GAAA;MACA,KAAAkF,MAAA,CAAAM,OAAA,6BAAAD,SAAA,aAAA9C,IAAA;QACA,WAAAgD,yBAAA,EAAAF,SAAA;MACA,GAAA9C,IAAA;QACA6C,MAAA,CAAAnD,OAAA;QACAmD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAvC,KAAA;IACA;IACA,eACA8C,gBAAA,WAAAA,iBAAAjB,GAAA;MAAA,IAAAkB,MAAA;MACA,KAAA/E,aAAA,GAAA6D,GAAA;MACA,KAAA9D,iBAAA;MACA,KAAAE,cAAA;;MAEA,IAAA4D,GAAA,CAAA5B,UAAA;QACA;UACA,KAAAlC,iBAAA,GAAAmC,IAAA,CAAAC,KAAA,CAAA0B,GAAA,CAAA5B,UAAA;UACA;UACA,KAAAlC,iBAAA,CAAAiF,OAAA,WAAAC,KAAA;YACA,IAAAA,KAAA,CAAAC,IAAA,iBAAAD,KAAA,CAAAE,OAAA;cACA,IAAAC,QAAA,GAAAH,KAAA,CAAAlG,IAAA,cAAAsG,IAAA,CAAAC,GAAA;cACAP,MAAA,CAAA9E,cAAA,CAAAmF,QAAA,IAAAL,MAAA,CAAAQ,gBAAA,CAAAN,KAAA,CAAAE,OAAA;YACA;UACA;QACA,SAAA/C,CAAA;UACA,KAAArC,iBAAA;QACA;MACA;MACA,KAAAF,cAAA;IACA;IACA,aACA2F,aAAA,WAAAA,cAAA3B,GAAA;MACA,KAAA7D,aAAA,GAAA6D,GAAA;MACA,KAAA9D,iBAAA;MACA,IAAA8D,GAAA,CAAA5B,UAAA;QACA;UACA,KAAAlC,iBAAA,GAAAmC,IAAA,CAAAC,KAAA,CAAA0B,GAAA,CAAA5B,UAAA;QACA,SAAAG,CAAA;UACA,KAAArC,iBAAA;QACA;MACA;MACA,KAAAD,oBAAA;IACA;IACA,aACA2F,YAAA,WAAAA,aAAA5B,GAAA;MAAA,IAAA6B,MAAA;MACA,KAAApB,MAAA,CAAAM,OAAA,2BAAA/C,IAAA;QACA,WAAA8D,4BAAA,EAAA9B,GAAA,CAAAnB,QAAA;MACA,GAAAb,IAAA;QACA6D,MAAA,CAAApE,iBAAA;QACAoE,MAAA,CAAAnE,OAAA;QACAmE,MAAA,CAAApB,MAAA,CAAAC,UAAA;MACA,GAAAvC,KAAA;IACA;IACA,aACA4D,YAAA,WAAAA,aAAA;MACA,IAAAC,WAAA,QAAAC,uBAAA;MACA,IAAAC,QAAA;QACAhH,IAAA,EAAA8G,WAAA;QACAG,KAAA;QACAd,IAAA;QACAhE,QAAA;QACAiE,OAAA;MACA;MACA,KAAApF,iBAAA,CAAAkG,IAAA,CAAAF,QAAA;IACA;IACA,aACAG,eAAA,WAAAA,gBAAAC,KAAA;MACA,KAAApG,iBAAA,CAAAqG,MAAA,CAAAD,KAAA;IACA;IACA,cACAL,uBAAA,WAAAA,wBAAAO,MAAA;MACA,IAAAC,OAAA;MACA,IAAAvH,IAAA,GAAAsH,MAAA,GAAAC,OAAA;MACA,YAAAvG,iBAAA,CAAAwG,IAAA,WAAAtB,KAAA;QAAA,OAAAA,KAAA,CAAAlG,IAAA,KAAAA,IAAA;MAAA;QACAuH,OAAA;QACAvH,IAAA,GAAAsH,MAAA,GAAAC,OAAA;MACA;MACA,OAAAvH,IAAA;IACA;IACA,mBACAyH,WAAA,WAAAA,YAAAtB,IAAA;MACA,kGAAAuB,QAAA,CAAAvB,IAAA;IACA;IACA,aACAwB,qBAAA,WAAAA,sBAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,OAAA;QACA,KAAArC,MAAA,CAAAM,OAAA,cAAA/C,IAAA;UACA+E,MAAA,CAAA7G,iBAAA;QACA;QACA;MACA;MAEA,IAAA8G,SAAA;QACAC,KAAA,GACA;UAAAd,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,GACA;UAAAa,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,GACA;UAAAa,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,EACA;QACA4B,OAAA,GACA;UAAAf,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,GACA;UAAAa,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,GACA;UAAAa,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,GACA;UAAAa,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,GACA;UAAAa,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,GACA;UAAAa,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,GACA;UAAAa,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,GACA;UAAAa,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,GACA;UAAAa,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,GACA;UAAAa,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA,GACA;UAAAa,KAAA;UAAAjH,IAAA;UAAAmG,IAAA;UAAAhE,QAAA;UAAAiE,OAAA;QAAA;MAEA;MAEA,IAAA0B,SAAA,CAAAF,OAAA;QACA,KAAA5G,iBAAA,GAAA8G,SAAA,CAAAF,OAAA,EAAAnD,GAAA,WAAAyB,KAAA;UAAA,WAAA+B,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAhC,KAAA;YACAlG,IAAA,EAAA6H,MAAA,CAAAd,uBAAA,CAAAb,KAAA,CAAAe,KAAA,CAAAkB,WAAA;UAAA;QAAA,CACA;MACA;IACA;IACA,WACAC,iBAAA,WAAAA,kBAAA;MACA,KAAArH,oBAAA;IACA;IACA,aACAsH,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,UAAArH,aAAA;QACA,KAAAsE,MAAA,CAAAgD,QAAA;QACA;MACA;;MAEA;MACA,SAAAC,CAAA,MAAAA,CAAA,QAAAxH,iBAAA,CAAA2D,MAAA,EAAA6D,CAAA;QACA,IAAAtC,KAAA,QAAAlF,iBAAA,CAAAwH,CAAA;QACA,KAAAtC,KAAA,CAAAe,KAAA;UACA,KAAA1B,MAAA,CAAAgD,QAAA,UAAAE,MAAA,CAAAD,CAAA;UACA;QACA;QACA,KAAAtC,KAAA,CAAAlG,IAAA;UACAkG,KAAA,CAAAlG,IAAA,QAAA+G,uBAAA,CAAAb,KAAA,CAAAe,KAAA,CAAAkB,WAAA;QACA;QACA,SAAAV,WAAA,CAAAvB,KAAA,CAAAC,IAAA,MAAAD,KAAA,CAAAE,OAAA;UACA,KAAAb,MAAA,CAAAgD,QAAA,UAAAE,MAAA,CAAAD,CAAA,8BAAAC,MAAA,CAAAvC,KAAA,CAAAe,KAAA;UACA;QACA;MACA;MAEA,IAAAyB,QAAA;QACA/E,QAAA,OAAA1C,aAAA,CAAA0C,QAAA;QACAT,UAAA,EAAAC,IAAA,CAAAwF,SAAA,MAAA3H,iBAAA;MACA;MAEA,IAAAsE,4BAAA,EAAAoD,QAAA,EAAA5F,IAAA,WAAAC,QAAA;QACAuF,MAAA,CAAA/C,MAAA,CAAAC,UAAA;QACA8C,MAAA,CAAAxH,cAAA;QACAwH,MAAA,CAAA/F,iBAAA;QACA+F,MAAA,CAAA9F,OAAA;MACA;IACA;IACA,aACAoG,eAAA,WAAAA,gBAAAxC,OAAA;MACA,KAAAA,OAAA;MACA,OAAAA,OAAA,CAAAyC,KAAA,MAAApE,GAAA,WAAAqE,GAAA;QAAA,OAAAA,GAAA,CAAAC,IAAA;MAAA,GAAAC,MAAA,WAAAF,GAAA;QAAA,OAAAA,GAAA;MAAA;IACA;IACA,eACAtC,gBAAA,WAAAA,iBAAAJ,OAAA;MACA,KAAAA,OAAA;MACA;QACA,IAAA6C,MAAA,GAAA9F,IAAA,CAAAC,KAAA,CAAAgD,OAAA;QACA;QACA,OAAA8C,KAAA,CAAAC,OAAA,CAAAF,MAAA,IAAAA,MAAA;MACA,SAAA5F,CAAA;QACA;QACA,WAAA+C,OAAA,iBAAAA,OAAA,CAAA2C,IAAA;UACA,OAAA3C,OAAA,CAAAyC,KAAA,MAAApE,GAAA,WAAAqE,GAAA;YAAA;cACAM,IAAA,EAAAN,GAAA,CAAAC,IAAA;cACAM,QAAA;YACA;UAAA,GAAAL,MAAA,WAAAF,GAAA;YAAA,OAAAA,GAAA,CAAAM,IAAA;UAAA;QACA;QACA;MACA;IACA;IACA,eACAE,eAAA,WAAAA,gBAAApD,KAAA;MACA,IAAAG,QAAA,GAAAH,KAAA,CAAAlG,IAAA,cAAAsG,IAAA,CAAAC,GAAA;MACA,UAAArF,cAAA,CAAAmF,QAAA;QACA,KAAAnF,cAAA,CAAAmF,QAAA,SAAAG,gBAAA,CAAAN,KAAA,CAAAE,OAAA;MACA;MACA,KAAAlF,cAAA,CAAAmF,QAAA,EAAAa,IAAA;QACAkC,IAAA;QACAC,QAAA;MACA;MACA,KAAAE,2BAAA,CAAArD,KAAA;IACA;IACA,eACAsD,kBAAA,WAAAA,mBAAAtD,KAAA,EAAAkB,KAAA;MACA,IAAAf,QAAA,GAAAH,KAAA,CAAAlG,IAAA,cAAAsG,IAAA,CAAAC,GAAA;MACA,SAAArF,cAAA,CAAAmF,QAAA;QACA,KAAAnF,cAAA,CAAAmF,QAAA,EAAAgB,MAAA,CAAAD,KAAA;QACA,KAAAmC,2BAAA,CAAArD,KAAA;MACA;IACA;IACA,oBACAuD,uBAAA,WAAAA,wBAAAvD,KAAA;MACA,IAAAG,QAAA,GAAAH,KAAA,CAAAlG,IAAA,cAAAsG,IAAA,CAAAC,GAAA;MACA,UAAArF,cAAA,CAAAmF,QAAA;QACA;QACA,KAAAnF,cAAA,CAAAmF,QAAA,SAAAG,gBAAA,CAAAN,KAAA,CAAAE,OAAA;MACA;MACA,YAAAlF,cAAA,CAAAmF,QAAA;IACA;IACA,oBACAkD,2BAAA,WAAAA,4BAAArD,KAAA;MACA,IAAAG,QAAA,GAAAH,KAAA,CAAAlG,IAAA,cAAAsG,IAAA,CAAAC,GAAA;MACA,SAAArF,cAAA,CAAAmF,QAAA;QACAH,KAAA,CAAAE,OAAA,GAAAjD,IAAA,CAAAwF,SAAA,MAAAzH,cAAA,CAAAmF,QAAA;MACA;IACA;IACA,eACAqD,mBAAA,WAAAA,oBAAAxD,KAAA;MAAA,IAAAyD,MAAA;MACA;MACA,KAAAC,SAAA;QACA,IAAAC,aAAA,GAAAF,MAAA,CAAAnD,gBAAA,CAAAN,KAAA,CAAAE,OAAA;QACAF,KAAA,CAAAE,OAAA,GAAAjD,IAAA,CAAAwF,SAAA,CAAAkB,aAAA;MACA;IACA;IACA,eACAC,qBAAA,WAAAA,sBAAA5D,KAAA;MACA,IAAAG,QAAA,GAAAH,KAAA,CAAAlG,IAAA,cAAAsG,IAAA,CAAAC,GAAA;;MAEA;MACA,IAAAL,KAAA,CAAAC,IAAA;QACA,KAAAD,KAAA,CAAAE,OAAA,IAAAF,KAAA,CAAAE,OAAA;UACA,IAAA2D,cAAA,IACA;YAAAX,IAAA;YAAAC,QAAA;UAAA,GACA;YAAAD,IAAA;YAAAC,QAAA;UAAA,EACA;UACAnD,KAAA,CAAAE,OAAA,GAAAjD,IAAA,CAAAwF,SAAA,CAAAoB,cAAA;UACA,KAAA7I,cAAA,CAAAmF,QAAA,IAAA0D,cAAA;QACA;UACA;UACA,KAAA7I,cAAA,CAAAmF,QAAA,SAAAG,gBAAA,CAAAN,KAAA,CAAAE,OAAA;QACA;MACA;MACA;MAAA,KACA,SAAAqB,WAAA,CAAAvB,KAAA,CAAAC,IAAA,KAAAD,KAAA,CAAAC,IAAA,iBAAAD,KAAA,CAAAE,OAAA;QACA;QACA,YAAAlF,cAAA,CAAAmF,QAAA;QAEA;UACA,IAAA4C,MAAA,GAAA9F,IAAA,CAAAC,KAAA,CAAA8C,KAAA,CAAAE,OAAA;UACA,IAAA8C,KAAA,CAAAC,OAAA,CAAAF,MAAA;YACA/C,KAAA,CAAAE,OAAA,GAAA6C,MAAA,CAAAxE,GAAA,WAAAqE,GAAA;cAAA,OAAAA,GAAA,CAAAM,IAAA,IAAAN,GAAA,CAAA7B,KAAA,IAAA6B,GAAA;YAAA,GAAAkB,IAAA;UACA;QACA,SAAA3G,CAAA;UACA;QAAA;MAEA;QACA;QACA,YAAAnC,cAAA,CAAAmF,QAAA;MACA;IACA;IACA,iBACA4D,qBAAA,WAAAA,sBAAA9D,IAAA;MACA,IAAA+D,YAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,MAAA;MACA;MACA,OAAAP,YAAA,CAAA/D,IAAA;IACA;IACA,aACAuE,YAAA,WAAAA,aAAAvE,IAAA;MACA,IAAAwE,KAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,KAAA;QACAC,GAAA;QACAb,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,MAAA;QACAQ,IAAA;QACAC,IAAA;MACA;MACA,OAAAP,KAAA,CAAAxE,IAAA;IACA;IACA,eACAgF,gBAAA,WAAAA,iBAAAhF,IAAA;MACA,IAAAiF,KAAA;QACAR,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,KAAA;QACAC,GAAA;QACAb,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,MAAA;QACAQ,IAAA;QACAC,IAAA;MACA;MACA,OAAAE,KAAA,CAAAjF,IAAA;IACA;IACA,aACAkF,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,0CAAArD,cAAA,CAAAC,OAAA,MACA,KAAAtG,WAAA,iBAAA6G,MAAA,CACA,IAAAnC,IAAA,GAAAiF,OAAA;IACA;IACA,kBACAC,mBAAA,WAAAA,oBAAA;MACA,KAAAC,gBAAA;MACA,KAAAtK,oBAAA;IACA;IACA,cACAsK,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,2BAAA,IAAA7I,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAA7C,IAAA,IAAA6C,QAAA,CAAA7C,IAAA,CAAA0L,WAAA;UACAF,MAAA,CAAAtK,mBAAA,GAAA2B,QAAA,CAAA7C,IAAA,CAAA0L,WAAA;QACA;UACAF,MAAA,CAAAtK,mBAAA;QACA;MACA,GAAA6B,KAAA;QACAyI,MAAA,CAAAtK,mBAAA;MACA;IACA;IACA,YACAyK,mBAAA,WAAAA,oBAAAX,IAAA;MACA,IAAAY,OAAA,GAAAZ,IAAA,CAAA/E,IAAA,CAAA4F,OAAA;MACA,IAAAC,MAAA,GAAAd,IAAA,CAAAe,IAAA;MAEA,KAAAH,OAAA;QACA,KAAAvG,MAAA,CAAAgD,QAAA;QACA;MACA;MACA,KAAAyD,MAAA;QACA,KAAAzG,MAAA,CAAAgD,QAAA;QACA;MACA;MACA;IACA;IACA,aACA2D,0BAAA,WAAAA,2BAAAnJ,QAAA;MAAA,IAAAoJ,OAAA;MACA,IAAApJ,QAAA,CAAAqJ,IAAA;QACA,IAAAC,QAAA,GAAAtJ,QAAA,CAAAH,GAAA;QACA,IAAA0J,8BAAA,EAAAD,QAAA,EAAAvJ,IAAA;UACAqJ,OAAA,CAAA/K,mBAAA,GAAAiL,QAAA;UACAF,OAAA,CAAA5G,MAAA,CAAAC,UAAA;QACA,GAAAvC,KAAA;UACAkJ,OAAA,CAAA5G,MAAA,CAAAgD,QAAA;QACA;MACA;QACA,KAAAhD,MAAA,CAAAgD,QAAA,aAAAxF,QAAA,CAAAwJ,GAAA;MACA;IACA;IACA,aACAC,wBAAA,WAAAA,yBAAA;MACA,KAAAjH,MAAA,CAAAgD,QAAA;IACA;IACA,cACAkE,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,KAAAnH,MAAA,CAAAM,OAAA,iBAAA/C,IAAA;QACA,IAAAwJ,8BAAA,MAAAxJ,IAAA;UACA4J,OAAA,CAAAtL,mBAAA;UACAsL,OAAA,CAAAnH,MAAA,CAAAC,UAAA;QACA,GAAAvC,KAAA;UACAyJ,OAAA,CAAAnH,MAAA,CAAAgD,QAAA;QACA;MACA,GAAAtF,KAAA;IACA;EACA;AACA", "ignoreList": []}]}