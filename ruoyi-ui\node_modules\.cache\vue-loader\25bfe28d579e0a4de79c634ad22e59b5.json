{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue?vue&type=template&id=265cb072&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue", "mtime": 1754384002001}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}