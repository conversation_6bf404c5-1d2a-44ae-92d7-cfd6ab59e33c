{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectRegistration\\index.vue?vue&type=template&id=8e08a9e6&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectRegistration\\index.vue", "mtime": 1754384929304}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1751437916212}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}