{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectRegistration\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectRegistration\\index.vue", "mtime": 1754384929304}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}