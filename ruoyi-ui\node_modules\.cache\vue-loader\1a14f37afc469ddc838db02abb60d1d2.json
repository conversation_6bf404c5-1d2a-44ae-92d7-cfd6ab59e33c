{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectRegistration\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectRegistration\\index.vue", "mtime": 1754384929304}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UHJvamVjdFJlZ2lzdHJhdGlvbiwgZ2V0UHJvamVjdFJlZ2lzdHJhdGlvbiwgZGVsUHJvamVjdFJlZ2lzdHJhdGlvbiB9IGZyb20gIkAvYXBpL21pbmlhcHAvaGFpdGFuZy9wcm9qZWN0UmVnaXN0cmF0aW9uIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiUHJvamVjdFJlZ2lzdHJhdGlvbiIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3orrDlvZXooajmoLzmlbDmja4NCiAgICAgIHJlZ2lzdHJhdGlvbkxpc3Q6IFtdLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHVzZXJOYW1lOiBudWxsLA0KICAgICAgICB1c2VyUGhvbmU6IG51bGwNCiAgICAgIH0sDQogICAgICAvLyDmn6XnnIvooajljZXlj4LmlbANCiAgICAgIHZpZXdGb3JtOiB7fSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuafpeeci+W8ueWHuuWxgg0KICAgICAgdmlld09wZW46IGZhbHNlDQogICAgfTsNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvKiog6Kej5p6Q5ZCO55qE6KGo5Y2V5pWw5o2uICovDQogICAgcGFyc2VkRm9ybURhdGEoKSB7DQogICAgICBpZiAoIXRoaXMudmlld0Zvcm0uZm9ybURhdGEpIHJldHVybiBbXTsNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgZm9ybURhdGEgPSBKU09OLnBhcnNlKHRoaXMudmlld0Zvcm0uZm9ybURhdGEpOw0KDQogICAgICAgIC8vIOajgOafpeaVsOaNruagvOW8j++8muWmguaenOaYr+aVsOe7hOagvOW8j++8iOaWsOagvOW8j++8ie+8jOebtOaOpeWkhOeQhg0KICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShmb3JtRGF0YSkpIHsNCiAgICAgICAgICByZXR1cm4gZm9ybURhdGEubWFwKGZpZWxkID0+ICh7DQogICAgICAgICAgICBuYW1lOiBmaWVsZC5uYW1lLA0KICAgICAgICAgICAgbGFiZWw6IGZpZWxkLmxhYmVsIHx8IHRoaXMuZ2V0RGVmYXVsdEZpZWxkTGFiZWwoZmllbGQubmFtZSkgfHwgZmllbGQubmFtZSwNCiAgICAgICAgICAgIHZhbHVlOiB0aGlzLmZvcm1hdEZpZWxkVmFsdWUoZmllbGQudmFsdWUsIGZpZWxkLnR5cGUpLA0KICAgICAgICAgICAgdHlwZTogZmllbGQudHlwZSB8fCAnaW5wdXQnLA0KICAgICAgICAgICAgZmlsZXM6IGZpZWxkLnR5cGUgPT09ICdmaWxlJyA/IHRoaXMucGFyc2VGaWxlVmFsdWUoZmllbGQudmFsdWUpIDogbnVsbCwNCiAgICAgICAgICAgIHJlcXVpcmVkOiBmaWVsZC5yZXF1aXJlZCB8fCBmYWxzZSwNCiAgICAgICAgICAgIG9wdGlvbnM6IGZpZWxkLm9wdGlvbnMgfHwgJycNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDlhbzlrrnml6fmoLzlvI/vvJrlr7nosaHmoLzlvI/nmoTmlbDmja4NCiAgICAgICAgY29uc3QgY29uZmlnRGF0YSA9IHRoaXMudmlld0Zvcm0uY29uZmlnRGF0YSA/IEpTT04ucGFyc2UodGhpcy52aWV3Rm9ybS5jb25maWdEYXRhKSA6IFtdOw0KDQogICAgICAgIC8vIOWIm+W7uuWtl+autemFjee9ruaYoOWwhA0KICAgICAgICBjb25zdCBmaWVsZENvbmZpZ01hcCA9IHt9Ow0KICAgICAgICBjb25maWdEYXRhLmZvckVhY2goZmllbGQgPT4gew0KICAgICAgICAgIGZpZWxkQ29uZmlnTWFwW2ZpZWxkLm5hbWVdID0gZmllbGQ7DQogICAgICAgIH0pOw0KDQogICAgICAgIC8vIOino+aekOihqOWNleaVsOaNrg0KICAgICAgICBjb25zdCBwYXJzZWREYXRhID0gW107DQogICAgICAgIGZvciAoY29uc3QgW2ZpZWxkTmFtZSwgZmllbGRWYWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoZm9ybURhdGEpKSB7DQogICAgICAgICAgY29uc3QgZmllbGRDb25maWcgPSBmaWVsZENvbmZpZ01hcFtmaWVsZE5hbWVdIHx8IHt9Ow0KDQogICAgICAgICAgLy8g5LyY5YWI5L2/55So6YWN572u5Lit55qE5Lit5paH5qCH562+77yM5aaC5p6c5rKh5pyJ5YiZ5L2/55So5a2X5q615ZCNDQogICAgICAgICAgY29uc3QgZGlzcGxheUxhYmVsID0gZmllbGRDb25maWcubGFiZWwgfHwgdGhpcy5nZXREZWZhdWx0RmllbGRMYWJlbChmaWVsZE5hbWUpIHx8IGZpZWxkTmFtZTsNCg0KICAgICAgICAgIHBhcnNlZERhdGEucHVzaCh7DQogICAgICAgICAgICBuYW1lOiBmaWVsZE5hbWUsDQogICAgICAgICAgICBsYWJlbDogZGlzcGxheUxhYmVsLA0KICAgICAgICAgICAgdmFsdWU6IHRoaXMuZm9ybWF0RmllbGRWYWx1ZShmaWVsZFZhbHVlLCBmaWVsZENvbmZpZy50eXBlKSwNCiAgICAgICAgICAgIHR5cGU6IGZpZWxkQ29uZmlnLnR5cGUgfHwgJ2lucHV0JywNCiAgICAgICAgICAgIGZpbGVzOiBmaWVsZENvbmZpZy50eXBlID09PSAnZmlsZScgPyB0aGlzLnBhcnNlRmlsZVZhbHVlKGZpZWxkVmFsdWUpIDogbnVsbA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQoNCiAgICAgICAgcmV0dXJuIHBhcnNlZERhdGE7DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOihqOWNleaVsOaNruWksei0pTonLCBlKTsNCiAgICAgICAgcmV0dXJuIFtdOw0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6LlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3orrDlvZXliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RQcm9qZWN0UmVnaXN0cmF0aW9uKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnJlZ2lzdHJhdGlvbkxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ucmVnaXN0cmF0aW9uSWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUoKSB7DQogICAgICAvLyDov5nph4zlj6/ku6Xmt7vliqDkv67mlLnpgLvovpHvvIzmmoLml7bkuI3lrp7njrANCiAgICAgIHRoaXMuJG1vZGFsLm1zZ0luZm8oIuS/ruaUueWKn+iDveaaguacquWunueOsCIpOw0KICAgIH0sDQogICAgLyoqIOafpeeci+aMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVZpZXcocm93KSB7DQogICAgICBnZXRQcm9qZWN0UmVnaXN0cmF0aW9uKHJvdy5yZWdpc3RyYXRpb25JZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMudmlld0Zvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLnZpZXdPcGVuID0gdHJ1ZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgcmVnaXN0cmF0aW9uSWRzID0gcm93LnJlZ2lzdHJhdGlvbklkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5aSp5aSn5rW35qOg5p2v6aG555uu5oql5ZCN6K6w5b2V57yW5Y+35Li6IicgKyByZWdpc3RyYXRpb25JZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxQcm9qZWN0UmVnaXN0cmF0aW9uKHJlZ2lzdHJhdGlvbklkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ21pbmlhcHAvaGFpdGFuZy9yZWdpc3RyYXRpb24vZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgcHJvamVjdFJlZ2lzdHJhdGlvbl8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9LA0KICAgIC8qKiDmoLzlvI/ljJblrZfmrrXlgLwgKi8NCiAgICBmb3JtYXRGaWVsZFZhbHVlKHZhbHVlLCBmaWVsZFR5cGUpIHsNCiAgICAgIC8vIOWkhOeQhuepuuWAvA0KICAgICAgaWYgKHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSB1bmRlZmluZWQgfHwgdmFsdWUgPT09ICcnKSB7DQogICAgICAgIHJldHVybiAnJzsNCiAgICAgIH0NCg0KICAgICAgc3dpdGNoIChmaWVsZFR5cGUpIHsNCiAgICAgICAgY2FzZSAnY2hlY2tib3gnOg0KICAgICAgICBjYXNlICdjaGVja2JveF9vdGhlcic6DQogICAgICAgICAgLy8g5aSa6YCJ5YC86YCa5bi45piv5pWw57uEDQogICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7DQogICAgICAgICAgICByZXR1cm4gdmFsdWUuZmlsdGVyKHYgPT4gdiAhPT0gbnVsbCAmJiB2ICE9PSB1bmRlZmluZWQgJiYgdiAhPT0gJycpOw0KICAgICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJykgew0KICAgICAgICAgICAgLy8g5aaC5p6c5piv5a2X56ym5Liy77yM5bCd6K+V5oyJ6YCX5Y+35YiG5YmyDQogICAgICAgICAgICByZXR1cm4gdmFsdWUuc3BsaXQoJywnKS5tYXAodiA9PiB2LnRyaW0oKSkuZmlsdGVyKHYgPT4gdiAhPT0gJycpOw0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gW3ZhbHVlXTsNCiAgICAgICAgY2FzZSAncmFkaW8nOg0KICAgICAgICBjYXNlICdyYWRpb19vdGhlcic6DQogICAgICAgIGNhc2UgJ3NlbGVjdCc6DQogICAgICAgIGNhc2UgJ3NlbGVjdF9vdGhlcic6DQogICAgICAgIGNhc2UgJ3BpY2tlcic6DQogICAgICAgICAgLy8g5Y2V6YCJ5YC877yM5aSE55CG56m65a2X56ym5Liy55qE5oOF5Ya1DQogICAgICAgICAgcmV0dXJuIHZhbHVlICYmIHZhbHVlICE9PSAnJyA/IFN0cmluZyh2YWx1ZSkgOiAnJzsNCiAgICAgICAgY2FzZSAnZmlsZSc6DQogICAgICAgICAgLy8g5paH5Lu257G75Z6L54m55q6K5aSE55CGDQogICAgICAgICAgcmV0dXJuIHZhbHVlID8gJ+afpeeci+aWh+S7ticgOiAnJzsNCiAgICAgICAgY2FzZSAnZGF0ZSc6DQogICAgICAgICAgLy8g5pel5pyf5qC85byP5YyWDQogICAgICAgICAgaWYgKHZhbHVlICYmIHZhbHVlICE9PSAnJykgew0KICAgICAgICAgICAgLy8g5aaC5p6c5piv5qCH5YeG5pel5pyf5qC85byP77yM55u05o6l6L+U5ZueDQogICAgICAgICAgICBpZiAoL15cZHs0fS1cZHsyfS1cZHsyfSQvLnRlc3QodmFsdWUpKSB7DQogICAgICAgICAgICAgIHJldHVybiB2YWx1ZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIC8vIOWQpuWImeWwneivleagvOW8j+WMlg0KICAgICAgICAgICAgcmV0dXJuIHRoaXMucGFyc2VUaW1lID8gdGhpcy5wYXJzZVRpbWUodmFsdWUsICd7eX0te219LXtkfScpIDogdmFsdWU7DQogICAgICAgICAgfQ0KICAgICAgICAgIHJldHVybiAnJzsNCiAgICAgICAgY2FzZSAnZGF0ZXRpbWUnOg0KICAgICAgICAgIC8vIOaXpeacn+aXtumXtOagvOW8j+WMlg0KICAgICAgICAgIGlmICh2YWx1ZSAmJiB2YWx1ZSAhPT0gJycpIHsNCiAgICAgICAgICAgIHJldHVybiB0aGlzLnBhcnNlVGltZSA/IHRoaXMucGFyc2VUaW1lKHZhbHVlLCAne3l9LXttfS17ZH0ge2h9OntpfTp7c30nKSA6IHZhbHVlOw0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gJyc7DQogICAgICAgIGNhc2UgJ251bWJlcic6DQogICAgICAgICAgLy8g5pWw5a2X5qC85byP5YyWDQogICAgICAgICAgcmV0dXJuIHZhbHVlID8gU3RyaW5nKHZhbHVlKSA6ICcnOw0KICAgICAgICBjYXNlICd0ZXh0YXJlYSc6DQogICAgICAgICAgLy8g5aSa6KGM5paH5pysDQogICAgICAgICAgcmV0dXJuIFN0cmluZyh2YWx1ZSk7DQogICAgICAgIGNhc2UgJ2lucHV0JzoNCiAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICByZXR1cm4gU3RyaW5nKHZhbHVlKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDop6PmnpDmlofku7blgLwgKi8NCiAgICBwYXJzZUZpbGVWYWx1ZSh2YWx1ZSkgew0KICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIFtdOw0KDQogICAgICB0cnkgew0KICAgICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJykgew0KICAgICAgICAgIC8vIOajgOafpeaYr+WQpuaYr1VSTOagvOW8j++8iOS7pWh0dHDlvIDlpLTvvIkNCiAgICAgICAgICBpZiAodmFsdWUuc3RhcnRzV2l0aCgnaHR0cDovLycpIHx8IHZhbHVlLnN0YXJ0c1dpdGgoJ2h0dHBzOi8vJykpIHsNCiAgICAgICAgICAgIC8vIOS7jlVSTOS4reaPkOWPluaWh+S7tuWQjQ0KICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSB2YWx1ZS5zcGxpdCgnLycpLnBvcCgpIHx8ICfmlofku7YnOw0KICAgICAgICAgICAgcmV0dXJuIFt7IG5hbWU6IGZpbGVOYW1lLCB1cmw6IHZhbHVlIH1dOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOWwneivleino+aekEpTT07lrZfnrKbkuLINCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZSh2YWx1ZSk7DQogICAgICAgICAgICByZXR1cm4gQXJyYXkuaXNBcnJheShwYXJzZWQpID8gcGFyc2VkIDogW3BhcnNlZF07DQogICAgICAgICAgfSBjYXRjaCAoanNvbkVycm9yKSB7DQogICAgICAgICAgICAvLyDlpoLmnpzkuI3mmK9KU09O77yM5b2T5L2c5pmu6YCa5paH5Lu25ZCN5aSE55CGDQogICAgICAgICAgICByZXR1cm4gW3sgbmFtZTogdmFsdWUsIHVybDogdmFsdWUgfV07DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7DQogICAgICAgICAgcmV0dXJuIHZhbHVlOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHJldHVybiBbdmFsdWVdOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIC8vIOWmguaenOino+aekOWksei0pe+8jOW9k+S9nOaZrumAmuWtl+espuS4suWkhOeQhg0KICAgICAgICByZXR1cm4gW3sgbmFtZTogdmFsdWUsIHVybDogdmFsdWUgfV07DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bpu5jorqTlrZfmrrXmoIfnrb4gKi8NCiAgICBnZXREZWZhdWx0RmllbGRMYWJlbChmaWVsZE5hbWUpIHsNCiAgICAgIGNvbnN0IGxhYmVsTWFwID0gew0KICAgICAgICAvLyDpobnnm67nm7jlhbMNCiAgICAgICAgJ3Byb2plY3ROYW1lJzogJ+mhueebruWQjeensCcsDQogICAgICAgICdwcm9qZWN0RGVzYyc6ICfpobnnm67nroDku4snLA0KICAgICAgICAncHJvamVjdERlc2NyaXB0aW9uJzogJ+mhueebruaPj+i/sCcsDQogICAgICAgICdwcm9qZWN0VHlwZSc6ICfpobnnm67nsbvlnosnLA0KICAgICAgICAncHJvamVjdFN0YWdlJzogJ+mhueebrumYtuautScsDQogICAgICAgICdwcm9qZWN0Q2F0ZWdvcnknOiAn6aG555uu5YiG57G7JywNCg0KICAgICAgICAvLyDlm6LpmJ/nm7jlhbMNCiAgICAgICAgJ3RlYW1MZWFkZXInOiAn5Zui6Zif6LSf6LSj5Lq6JywNCiAgICAgICAgJ3RlYW1TaXplJzogJ+WboumYn+inhOaooScsDQogICAgICAgICd0ZWFtTWVtYmVyJzogJ+WboumYn+aIkOWRmCcsDQogICAgICAgICd0ZWFtRGVzY3JpcHRpb24nOiAn5Zui6Zif5LuL57uNJywNCg0KICAgICAgICAvLyDogZTns7vkv6Hmga8NCiAgICAgICAgJ25hbWUnOiAn5aeT5ZCNJywNCiAgICAgICAgJ3VzZXJOYW1lJzogJ+eUqOaIt+Wnk+WQjScsDQogICAgICAgICdwaG9uZSc6ICfogZTns7vnlLXor50nLA0KICAgICAgICAndXNlclBob25lJzogJ+eUqOaIt+aJi+acuuWPtycsDQogICAgICAgICdlbWFpbCc6ICfpgq7nrrHlnLDlnYAnLA0KICAgICAgICAndXNlckVtYWlsJzogJ+eUqOaIt+mCrueusScsDQogICAgICAgICdhZGRyZXNzJzogJ+iBlOezu+WcsOWdgCcsDQogICAgICAgICdjb21wYW55JzogJ+aJgOWcqOWFrOWPuCcsDQogICAgICAgICdwb3NpdGlvbic6ICfogYzkvY0nLA0KDQogICAgICAgIC8vIOi6q+S7veS/oeaBrw0KICAgICAgICAnaWRjYXJkJzogJ+i6q+S7veivgeWPtycsDQogICAgICAgICdzdHVkZW50SWQnOiAn5a2m5Y+3JywNCiAgICAgICAgJ3dvcmtJZCc6ICflt6Xlj7cnLA0KDQogICAgICAgIC8vIOaXtumXtOebuOWFsw0KICAgICAgICAncmVnaXN0cmF0aW9uRGF0ZSc6ICfmiqXlkI3ml6XmnJ8nLA0KICAgICAgICAncmVnaXN0cmF0aW9uVGltZSc6ICfmiqXlkI3ml7bpl7QnLA0KICAgICAgICAnc3RhcnREYXRlJzogJ+W8gOWni+aXpeacnycsDQogICAgICAgICdlbmREYXRlJzogJ+e7k+adn+aXpeacnycsDQogICAgICAgICdiaXJ0aERhdGUnOiAn5Ye655Sf5pel5pyfJywNCg0KICAgICAgICAvLyDmlofku7bnm7jlhbMNCiAgICAgICAgJ3BsYW5GaWxlJzogJ+mhueebruiuoeWIkuS5picsDQogICAgICAgICd2aWRlb0ZpbGUnOiAn5ryU56S66KeG6aKRJywNCiAgICAgICAgJ3Jlc3VtZUZpbGUnOiAn5Liq5Lq6566A5Y6GJywNCiAgICAgICAgJ2NlcnRpZmljYXRlRmlsZSc6ICfor4Hkuabmlofku7YnLA0KICAgICAgICAnYXR0YWNobWVudEZpbGUnOiAn6ZmE5Lu25paH5Lu2JywNCg0KICAgICAgICAvLyDpgInmi6nnm7jlhbMNCiAgICAgICAgJ2dlbmRlcic6ICfmgKfliKsnLA0KICAgICAgICAnZWR1Y2F0aW9uJzogJ+WtpuWOhicsDQogICAgICAgICdleHBlcmllbmNlJzogJ+W3peS9nOe7j+mqjCcsDQogICAgICAgICdza2lsbCc6ICfmioDog70nLA0KICAgICAgICAnaW50ZXJlc3QnOiAn5YW06Laj54ix5aW9JywNCiAgICAgICAgJ2hvYmJ5JzogJ+eIseWlvScsDQoNCiAgICAgICAgLy8g5Lia5Yqh55u45YWzDQogICAgICAgICdpbmR1c3RyeSc6ICfmiYDlsZ7ooYzkuJonLA0KICAgICAgICAnZnVuZGluZ1JvdW5kJzogJ+iejei1hOi9ruasoScsDQogICAgICAgICdpbnZlc3RtZW50JzogJ+aKlei1hOmHkeminScsDQogICAgICAgICdyZXZlbnVlJzogJ+iQpeaUtuaDheWGtScsDQogICAgICAgICd3ZWJzaXRlJzogJ+e9keermeWcsOWdgCcsDQogICAgICAgICdzb2NpYWxNZWRpYSc6ICfnpL7kuqTlqpLkvZMnLA0KDQogICAgICAgIC8vIOWFtuS7lg0KICAgICAgICAncmVtYXJrJzogJ+Wkh+azqCcsDQogICAgICAgICdjb21tZW50JzogJ+ivhOiuuicsDQogICAgICAgICdmZWVkYmFjayc6ICflj43ppognLA0KICAgICAgICAnc3VnZ2VzdGlvbic6ICflu7rorq4nLA0KICAgICAgICAncmVhc29uJzogJ+WOn+WboCcsDQogICAgICAgICdwdXJwb3NlJzogJ+ebrueahCcsDQogICAgICAgICdnb2FsJzogJ+ebruaghycsDQogICAgICAgICdwbGFuJzogJ+iuoeWIkicsDQogICAgICAgICdidWRnZXQnOiAn6aKE566XJywNCiAgICAgICAgJ3JlcXVpcmVtZW50JzogJ+mcgOaxgicsDQogICAgICAgICdleHBlY3RhdGlvbic6ICfmnJ/mnJsnDQogICAgICB9Ow0KDQogICAgICByZXR1cm4gbGFiZWxNYXBbZmllbGROYW1lXSB8fCBudWxsOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5a2X5q6157G75Z6L5qCH562+ICovDQogICAgZ2V0RmllbGRUeXBlTGFiZWwoZmllbGRUeXBlKSB7DQogICAgICBjb25zdCB0eXBlTGFiZWxzID0gew0KICAgICAgICAnaW5wdXQnOiAn5paH5pysJywNCiAgICAgICAgJ3RleHRhcmVhJzogJ+WkmuihjOaWh+acrCcsDQogICAgICAgICdyYWRpbyc6ICfljZXpgIknLA0KICAgICAgICAncmFkaW9fb3RoZXInOiAn5Y2V6YCJK+WFtuS7licsDQogICAgICAgICdjaGVja2JveCc6ICflpJrpgIknLA0KICAgICAgICAnY2hlY2tib3hfb3RoZXInOiAn5aSa6YCJK+WFtuS7licsDQogICAgICAgICdzZWxlY3QnOiAn5LiL5ouJ6YCJ5oupJywNCiAgICAgICAgJ3NlbGVjdF9vdGhlcic6ICfkuIvmi4kr5YW25LuWJywNCiAgICAgICAgJ3BpY2tlcic6ICfmu5rliqjpgInmi6knLA0KICAgICAgICAnZGF0ZSc6ICfml6XmnJ8nLA0KICAgICAgICAnZGF0ZXRpbWUnOiAn5pel5pyf5pe26Ze0JywNCiAgICAgICAgJ3RpbWUnOiAn5pe26Ze0JywNCiAgICAgICAgJ251bWJlcic6ICfmlbDlrZcnLA0KICAgICAgICAnZmlsZSc6ICfmlofku7YnLA0KICAgICAgICAnaW1hZ2UnOiAn5Zu+54mHJw0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTGFiZWxzW2ZpZWxkVHlwZV0gfHwgZmllbGRUeXBlOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5a2X5q6157G75Z6L5qCH562+6aKc6ImyICovDQogICAgZ2V0RmllbGRUeXBlVGFnVHlwZShmaWVsZFR5cGUpIHsNCiAgICAgIGNvbnN0IHR5cGVDb2xvcnMgPSB7DQogICAgICAgICdpbnB1dCc6ICcnLA0KICAgICAgICAndGV4dGFyZWEnOiAnaW5mbycsDQogICAgICAgICdyYWRpbyc6ICdzdWNjZXNzJywNCiAgICAgICAgJ3JhZGlvX290aGVyJzogJ3N1Y2Nlc3MnLA0KICAgICAgICAnY2hlY2tib3gnOiAnd2FybmluZycsDQogICAgICAgICdjaGVja2JveF9vdGhlcic6ICd3YXJuaW5nJywNCiAgICAgICAgJ3NlbGVjdCc6ICdwcmltYXJ5JywNCiAgICAgICAgJ3NlbGVjdF9vdGhlcic6ICdwcmltYXJ5JywNCiAgICAgICAgJ3BpY2tlcic6ICdwcmltYXJ5JywNCiAgICAgICAgJ2RhdGUnOiAnaW5mbycsDQogICAgICAgICdkYXRldGltZSc6ICdpbmZvJywNCiAgICAgICAgJ3RpbWUnOiAnaW5mbycsDQogICAgICAgICdudW1iZXInOiAnc3VjY2VzcycsDQogICAgICAgICdmaWxlJzogJ2RhbmdlcicsDQogICAgICAgICdpbWFnZSc6ICdkYW5nZXInDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHR5cGVDb2xvcnNbZmllbGRUeXBlXSB8fCAnJzsNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmNA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/miniapp/haitang/projectRegistration", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"用户姓名\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          placeholder=\"请输入用户姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"用户手机号\" prop=\"userPhone\">\r\n        <el-input\r\n          v-model=\"queryParams.userPhone\"\r\n          placeholder=\"请输入用户手机号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['miniapp:haitang:registration:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['miniapp:haitang:registration:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['miniapp:haitang:registration:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"registrationList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"报名ID\" align=\"center\" prop=\"registrationId\" />\r\n      <el-table-column label=\"表单配置\" align=\"center\" prop=\"configName\" />\r\n      <el-table-column label=\"用户姓名\" align=\"center\" prop=\"userName\" />\r\n      <el-table-column label=\"用户手机号\" align=\"center\" prop=\"userPhone\" />\r\n      <el-table-column label=\"报名时间\" align=\"center\" prop=\"registrationTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleView(scope.row)\"\r\n            v-hasPermi=\"['miniapp:haitang:registration:query']\"\r\n          >查看</el-button>\r\n\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['miniapp:haitang:registration:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 查看报名详情对话框 -->\r\n    <el-dialog title=\"报名详情\" :visible.sync=\"viewOpen\" width=\"900px\" append-to-body>\r\n      <div class=\"registration-detail\">\r\n        <!-- 基本信息 -->\r\n        <div class=\"basic-info\">\r\n          <h3>基本信息</h3>\r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>表单配置：</label>\r\n                <span>{{ viewForm.configName }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>报名时间：</label>\r\n                <span>{{ parseTime(viewForm.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>用户姓名：</label>\r\n                <span>{{ viewForm.userName }}</span>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div class=\"info-item\">\r\n                <label>用户手机号：</label>\r\n                <span>{{ viewForm.userPhone }}</span>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n\r\n        <!-- 报名表单数据 -->\r\n        <div class=\"form-data\">\r\n          <h3>报名表单数据</h3>\r\n          <el-table :data=\"parsedFormData\" border style=\"width: 100%\">\r\n            <el-table-column prop=\"label\" label=\"字段名称\" width=\"180\" />\r\n            <el-table-column prop=\"type\" label=\"字段类型\" width=\"100\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-tag size=\"mini\" :type=\"getFieldTypeTagType(scope.row.type)\">\r\n                  {{ getFieldTypeLabel(scope.row.type) }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"value\" label=\"填写内容\" min-width=\"400\">\r\n              <template slot-scope=\"scope\">\r\n                <!-- 文件类型显示 -->\r\n                <div v-if=\"scope.row.type === 'file'\" class=\"file-display\">\r\n                  <div v-if=\"scope.row.files && scope.row.files.length > 0\">\r\n                    <div v-for=\"(file, index) in scope.row.files\" :key=\"index\" class=\"file-item\">\r\n                      <i class=\"el-icon-document\"></i>\r\n                      <a :href=\"file.url\" target=\"_blank\" class=\"file-link\">{{ file.name }}</a>\r\n                    </div>\r\n                  </div>\r\n                  <span v-else class=\"empty-value\">未上传文件</span>\r\n                </div>\r\n                <!-- 多选类型显示（数组） -->\r\n                <div v-else-if=\"Array.isArray(scope.row.value) && scope.row.value.length > 0\" class=\"array-display\">\r\n                  <el-tag v-for=\"(item, index) in scope.row.value\" :key=\"index\" size=\"small\" style=\"margin-right: 5px;\">\r\n                    {{ item }}\r\n                  </el-tag>\r\n                </div>\r\n                <!-- 多行文本显示 -->\r\n                <div v-else-if=\"scope.row.type === 'textarea' && scope.row.value\" class=\"textarea-display\">\r\n                  <div class=\"textarea-content\">{{ scope.row.value }}</div>\r\n                </div>\r\n                <!-- 选择类型显示（带标签） -->\r\n                <div v-else-if=\"['radio', 'select', 'picker', 'select_other', 'radio_other'].includes(scope.row.type) && scope.row.value\" class=\"select-display\">\r\n                  <el-tag size=\"small\" type=\"success\">{{ scope.row.value }}</el-tag>\r\n                </div>\r\n                <!-- 日期类型显示 -->\r\n                <div v-else-if=\"scope.row.type === 'date' && scope.row.value\" class=\"date-display\">\r\n                  <i class=\"el-icon-date\" style=\"margin-right: 5px;\"></i>\r\n                  <span>{{ scope.row.value }}</span>\r\n                </div>\r\n                <!-- 普通文本显示 -->\r\n                <span v-else-if=\"scope.row.value !== null && scope.row.value !== undefined && scope.row.value !== ''\" class=\"text-value\">{{ scope.row.value }}</span>\r\n                <!-- 空值显示 -->\r\n                <span v-else class=\"empty-value\">\r\n                  <i class=\"el-icon-minus\" style=\"margin-right: 3px;\"></i>未填写\r\n                </span>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n\r\n        <!-- 原始数据（可折叠） -->\r\n        <div class=\"raw-data\">\r\n          <el-collapse>\r\n            <el-collapse-item title=\"查看原始JSON数据\" name=\"rawData\">\r\n              <el-input v-model=\"viewForm.formData\" type=\"textarea\" :rows=\"8\" readonly />\r\n            </el-collapse-item>\r\n          </el-collapse>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"viewOpen = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listProjectRegistration, getProjectRegistration, delProjectRegistration } from \"@/api/miniapp/haitang/projectRegistration\";\r\n\r\nexport default {\r\n  name: \"ProjectRegistration\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 天大海棠杯项目报名记录表格数据\r\n      registrationList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userName: null,\r\n        userPhone: null\r\n      },\r\n      // 查看表单参数\r\n      viewForm: {},\r\n      // 是否显示查看弹出层\r\n      viewOpen: false\r\n    };\r\n  },\r\n  computed: {\r\n    /** 解析后的表单数据 */\r\n    parsedFormData() {\r\n      if (!this.viewForm.formData) return [];\r\n\r\n      try {\r\n        const formData = JSON.parse(this.viewForm.formData);\r\n\r\n        // 检查数据格式：如果是数组格式（新格式），直接处理\r\n        if (Array.isArray(formData)) {\r\n          return formData.map(field => ({\r\n            name: field.name,\r\n            label: field.label || this.getDefaultFieldLabel(field.name) || field.name,\r\n            value: this.formatFieldValue(field.value, field.type),\r\n            type: field.type || 'input',\r\n            files: field.type === 'file' ? this.parseFileValue(field.value) : null,\r\n            required: field.required || false,\r\n            options: field.options || ''\r\n          }));\r\n        }\r\n\r\n        // 兼容旧格式：对象格式的数据\r\n        const configData = this.viewForm.configData ? JSON.parse(this.viewForm.configData) : [];\r\n\r\n        // 创建字段配置映射\r\n        const fieldConfigMap = {};\r\n        configData.forEach(field => {\r\n          fieldConfigMap[field.name] = field;\r\n        });\r\n\r\n        // 解析表单数据\r\n        const parsedData = [];\r\n        for (const [fieldName, fieldValue] of Object.entries(formData)) {\r\n          const fieldConfig = fieldConfigMap[fieldName] || {};\r\n\r\n          // 优先使用配置中的中文标签，如果没有则使用字段名\r\n          const displayLabel = fieldConfig.label || this.getDefaultFieldLabel(fieldName) || fieldName;\r\n\r\n          parsedData.push({\r\n            name: fieldName,\r\n            label: displayLabel,\r\n            value: this.formatFieldValue(fieldValue, fieldConfig.type),\r\n            type: fieldConfig.type || 'input',\r\n            files: fieldConfig.type === 'file' ? this.parseFileValue(fieldValue) : null\r\n          });\r\n        }\r\n\r\n        return parsedData;\r\n      } catch (e) {\r\n        console.error('解析表单数据失败:', e);\r\n        return [];\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询天大海棠杯项目报名记录列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listProjectRegistration(this.queryParams).then(response => {\r\n        this.registrationList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.registrationId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate() {\r\n      // 这里可以添加修改逻辑，暂时不实现\r\n      this.$modal.msgInfo(\"修改功能暂未实现\");\r\n    },\r\n    /** 查看按钮操作 */\r\n    handleView(row) {\r\n      getProjectRegistration(row.registrationId).then(response => {\r\n        this.viewForm = response.data;\r\n        this.viewOpen = true;\r\n      });\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const registrationIds = row.registrationId || this.ids;\r\n      this.$modal.confirm('是否确认删除天大海棠杯项目报名记录编号为\"' + registrationIds + '\"的数据项？').then(function() {\r\n        return delProjectRegistration(registrationIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/haitang/registration/export', {\r\n        ...this.queryParams\r\n      }, `projectRegistration_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 格式化字段值 */\r\n    formatFieldValue(value, fieldType) {\r\n      // 处理空值\r\n      if (value === null || value === undefined || value === '') {\r\n        return '';\r\n      }\r\n\r\n      switch (fieldType) {\r\n        case 'checkbox':\r\n        case 'checkbox_other':\r\n          // 多选值通常是数组\r\n          if (Array.isArray(value)) {\r\n            return value.filter(v => v !== null && v !== undefined && v !== '');\r\n          } else if (typeof value === 'string') {\r\n            // 如果是字符串，尝试按逗号分割\r\n            return value.split(',').map(v => v.trim()).filter(v => v !== '');\r\n          }\r\n          return [value];\r\n        case 'radio':\r\n        case 'radio_other':\r\n        case 'select':\r\n        case 'select_other':\r\n        case 'picker':\r\n          // 单选值，处理空字符串的情况\r\n          return value && value !== '' ? String(value) : '';\r\n        case 'file':\r\n          // 文件类型特殊处理\r\n          return value ? '查看文件' : '';\r\n        case 'date':\r\n          // 日期格式化\r\n          if (value && value !== '') {\r\n            // 如果是标准日期格式，直接返回\r\n            if (/^\\d{4}-\\d{2}-\\d{2}$/.test(value)) {\r\n              return value;\r\n            }\r\n            // 否则尝试格式化\r\n            return this.parseTime ? this.parseTime(value, '{y}-{m}-{d}') : value;\r\n          }\r\n          return '';\r\n        case 'datetime':\r\n          // 日期时间格式化\r\n          if (value && value !== '') {\r\n            return this.parseTime ? this.parseTime(value, '{y}-{m}-{d} {h}:{i}:{s}') : value;\r\n          }\r\n          return '';\r\n        case 'number':\r\n          // 数字格式化\r\n          return value ? String(value) : '';\r\n        case 'textarea':\r\n          // 多行文本\r\n          return String(value);\r\n        case 'input':\r\n        default:\r\n          return String(value);\r\n      }\r\n    },\r\n    /** 解析文件值 */\r\n    parseFileValue(value) {\r\n      if (!value) return [];\r\n\r\n      try {\r\n        if (typeof value === 'string') {\r\n          // 检查是否是URL格式（以http开头）\r\n          if (value.startsWith('http://') || value.startsWith('https://')) {\r\n            // 从URL中提取文件名\r\n            const fileName = value.split('/').pop() || '文件';\r\n            return [{ name: fileName, url: value }];\r\n          }\r\n\r\n          // 尝试解析JSON字符串\r\n          try {\r\n            const parsed = JSON.parse(value);\r\n            return Array.isArray(parsed) ? parsed : [parsed];\r\n          } catch (jsonError) {\r\n            // 如果不是JSON，当作普通文件名处理\r\n            return [{ name: value, url: value }];\r\n          }\r\n        } else if (Array.isArray(value)) {\r\n          return value;\r\n        } else {\r\n          return [value];\r\n        }\r\n      } catch (e) {\r\n        // 如果解析失败，当作普通字符串处理\r\n        return [{ name: value, url: value }];\r\n      }\r\n    },\r\n\r\n    /** 获取默认字段标签 */\r\n    getDefaultFieldLabel(fieldName) {\r\n      const labelMap = {\r\n        // 项目相关\r\n        'projectName': '项目名称',\r\n        'projectDesc': '项目简介',\r\n        'projectDescription': '项目描述',\r\n        'projectType': '项目类型',\r\n        'projectStage': '项目阶段',\r\n        'projectCategory': '项目分类',\r\n\r\n        // 团队相关\r\n        'teamLeader': '团队负责人',\r\n        'teamSize': '团队规模',\r\n        'teamMember': '团队成员',\r\n        'teamDescription': '团队介绍',\r\n\r\n        // 联系信息\r\n        'name': '姓名',\r\n        'userName': '用户姓名',\r\n        'phone': '联系电话',\r\n        'userPhone': '用户手机号',\r\n        'email': '邮箱地址',\r\n        'userEmail': '用户邮箱',\r\n        'address': '联系地址',\r\n        'company': '所在公司',\r\n        'position': '职位',\r\n\r\n        // 身份信息\r\n        'idcard': '身份证号',\r\n        'studentId': '学号',\r\n        'workId': '工号',\r\n\r\n        // 时间相关\r\n        'registrationDate': '报名日期',\r\n        'registrationTime': '报名时间',\r\n        'startDate': '开始日期',\r\n        'endDate': '结束日期',\r\n        'birthDate': '出生日期',\r\n\r\n        // 文件相关\r\n        'planFile': '项目计划书',\r\n        'videoFile': '演示视频',\r\n        'resumeFile': '个人简历',\r\n        'certificateFile': '证书文件',\r\n        'attachmentFile': '附件文件',\r\n\r\n        // 选择相关\r\n        'gender': '性别',\r\n        'education': '学历',\r\n        'experience': '工作经验',\r\n        'skill': '技能',\r\n        'interest': '兴趣爱好',\r\n        'hobby': '爱好',\r\n\r\n        // 业务相关\r\n        'industry': '所属行业',\r\n        'fundingRound': '融资轮次',\r\n        'investment': '投资金额',\r\n        'revenue': '营收情况',\r\n        'website': '网站地址',\r\n        'socialMedia': '社交媒体',\r\n\r\n        // 其他\r\n        'remark': '备注',\r\n        'comment': '评论',\r\n        'feedback': '反馈',\r\n        'suggestion': '建议',\r\n        'reason': '原因',\r\n        'purpose': '目的',\r\n        'goal': '目标',\r\n        'plan': '计划',\r\n        'budget': '预算',\r\n        'requirement': '需求',\r\n        'expectation': '期望'\r\n      };\r\n\r\n      return labelMap[fieldName] || null;\r\n    },\r\n\r\n    /** 获取字段类型标签 */\r\n    getFieldTypeLabel(fieldType) {\r\n      const typeLabels = {\r\n        'input': '文本',\r\n        'textarea': '多行文本',\r\n        'radio': '单选',\r\n        'radio_other': '单选+其他',\r\n        'checkbox': '多选',\r\n        'checkbox_other': '多选+其他',\r\n        'select': '下拉选择',\r\n        'select_other': '下拉+其他',\r\n        'picker': '滚动选择',\r\n        'date': '日期',\r\n        'datetime': '日期时间',\r\n        'time': '时间',\r\n        'number': '数字',\r\n        'file': '文件',\r\n        'image': '图片'\r\n      };\r\n      return typeLabels[fieldType] || fieldType;\r\n    },\r\n\r\n    /** 获取字段类型标签颜色 */\r\n    getFieldTypeTagType(fieldType) {\r\n      const typeColors = {\r\n        'input': '',\r\n        'textarea': 'info',\r\n        'radio': 'success',\r\n        'radio_other': 'success',\r\n        'checkbox': 'warning',\r\n        'checkbox_other': 'warning',\r\n        'select': 'primary',\r\n        'select_other': 'primary',\r\n        'picker': 'primary',\r\n        'date': 'info',\r\n        'datetime': 'info',\r\n        'time': 'info',\r\n        'number': 'success',\r\n        'file': 'danger',\r\n        'image': 'danger'\r\n      };\r\n      return typeColors[fieldType] || '';\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.registration-detail {\r\n  padding: 20px;\r\n}\r\n\r\n.basic-info {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.basic-info h3 {\r\n  margin: 0 0 20px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  border-bottom: 2px solid #409eff;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.info-item {\r\n  margin-bottom: 15px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.info-item label {\r\n  font-weight: 500;\r\n  color: #606266;\r\n  margin-right: 10px;\r\n  min-width: 80px;\r\n}\r\n\r\n.info-item span {\r\n  color: #303133;\r\n}\r\n\r\n.form-data {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.form-data h3 {\r\n  margin: 0 0 20px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  border-bottom: 2px solid #67c23a;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.file-display {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.file-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 12px;\r\n  background: #f5f7fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e4e7ed;\r\n}\r\n\r\n.file-link {\r\n  color: #409eff;\r\n  text-decoration: none;\r\n}\r\n\r\n.file-link:hover {\r\n  text-decoration: underline;\r\n}\r\n\r\n.array-display {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 5px;\r\n}\r\n\r\n.text-value {\r\n  color: #303133;\r\n  word-break: break-all;\r\n  line-height: 1.5;\r\n}\r\n\r\n.empty-value {\r\n  color: #c0c4cc;\r\n  font-style: italic;\r\n}\r\n\r\n.textarea-display {\r\n  max-width: 100%;\r\n}\r\n\r\n.textarea-content {\r\n  background: #f5f7fa;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  padding: 10px;\r\n  white-space: pre-wrap;\r\n  word-break: break-word;\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  font-size: 13px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.select-display {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.date-display {\r\n  display: flex;\r\n  align-items: center;\r\n  color: #606266;\r\n}\r\n\r\n.raw-data {\r\n  margin-top: 20px;\r\n}\r\n\r\n.raw-data .el-collapse {\r\n  border: none;\r\n}\r\n\r\n.raw-data .el-collapse-item__header {\r\n  background: #f5f7fa;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  padding: 0 15px;\r\n  font-size: 14px;\r\n  color: #606266;\r\n}\r\n\r\n.raw-data .el-collapse-item__content {\r\n  padding: 15px 0 0 0;\r\n}\r\n</style>\r\n"]}]}