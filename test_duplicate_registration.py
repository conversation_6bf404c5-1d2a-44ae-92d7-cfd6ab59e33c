#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路演报名和项目报名的重复提交检查功能
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8086"

def test_project_registration_duplicate():
    """测试项目报名重复提交检查"""
    print("=== 测试项目报名重复提交检查 ===")
    
    # 测试数据
    project_data = {
        "userId": 12345,
        "projectName": "测试项目",
        "teamSize": 5,
        "city": "天津",
        "competitionArea": "天津赛区",
        "industry": "科技",
        "isTjuAlumni": 1,
        "projectDescription": "这是一个测试项目",
        "hasCompany": 1,
        "contactName": "张三",
        "contactPhone": "13800138000",
        "contactWechat": "test_wechat",
        "contactPosition": "项目经理"
    }
    
    # 第一次提交
    print("1. 第一次提交项目报名...")
    response1 = requests.post(f"{BASE_URL}/miniapp/haitang/project/app/register", 
                             json=project_data,
                             headers={"Content-Type": "application/json"})
    
    print(f"状态码: {response1.status_code}")
    print(f"响应: {response1.text}")
    
    # 第二次提交（重复）
    print("\n2. 第二次提交项目报名（重复）...")
    response2 = requests.post(f"{BASE_URL}/miniapp/haitang/project/app/register", 
                             json=project_data,
                             headers={"Content-Type": "application/json"})
    
    print(f"状态码: {response2.status_code}")
    print(f"响应: {response2.text}")
    
    # 验证重复提交返回的消息
    if response2.status_code == 200:
        result = response2.json()
        if result.get("msg") == "报名成功，请勿重复提交报名":
            print("✅ 项目报名重复提交检查正常")
        else:
            print("❌ 项目报名重复提交检查异常")
    else:
        print("❌ 项目报名接口调用失败")

def test_roadshow_registration_duplicate():
    """测试路演报名重复提交检查"""
    print("\n=== 测试路演报名重复提交检查 ===")
    
    # 测试数据
    roadshow_data = {
        "activityId": 1,
        "userId": 12345,
        "formData": json.dumps({
            "name": "张三",
            "phone": "13800138000",
            "company": "测试公司",
            "position": "项目经理"
        })
    }
    
    # 第一次提交
    print("1. 第一次提交路演报名...")
    response1 = requests.post(f"{BASE_URL}/miniapp/xiqing/registration-manage/app/register", 
                             json=roadshow_data,
                             headers={"Content-Type": "application/json"})
    
    print(f"状态码: {response1.status_code}")
    print(f"响应: {response1.text}")
    
    # 第二次提交（重复）
    print("\n2. 第二次提交路演报名（重复）...")
    response2 = requests.post(f"{BASE_URL}/miniapp/xiqing/registration-manage/app/register", 
                             json=roadshow_data,
                             headers={"Content-Type": "application/json"})
    
    print(f"状态码: {response2.status_code}")
    print(f"响应: {response2.text}")
    
    # 验证重复提交返回的消息
    if response2.status_code == 200:
        result = response2.json()
        if result.get("msg") == "报名成功，请勿重复提交报名":
            print("✅ 路演报名重复提交检查正常")
        else:
            print("❌ 路演报名重复提交检查异常")
    else:
        print("❌ 路演报名接口调用失败")

def test_backend_list_access():
    """测试后端管理页面访问"""
    print("\n=== 测试后端管理页面访问 ===")
    
    # 测试项目报名列表
    print("1. 测试项目报名列表...")
    try:
        response = requests.get(f"{BASE_URL}/miniapp/haitang/project/list?pageNum=1&pageSize=10")
        print(f"项目报名列表 - 状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 项目报名列表访问正常")
        else:
            print("❌ 项目报名列表访问异常")
    except Exception as e:
        print(f"❌ 项目报名列表访问失败: {e}")
    
    # 测试路演报名列表
    print("\n2. 测试路演报名列表...")
    try:
        response = requests.get(f"{BASE_URL}/miniapp/xiqing/registration-manage/list?pageNum=1&pageSize=10")
        print(f"路演报名列表 - 状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 路演报名列表访问正常")
        else:
            print("❌ 路演报名列表访问异常")
    except Exception as e:
        print(f"❌ 路演报名列表访问失败: {e}")

if __name__ == "__main__":
    print("开始测试重复提交检查功能...")
    print(f"测试服务器: {BASE_URL}")
    print("=" * 50)
    
    try:
        # 测试项目报名重复提交
        test_project_registration_duplicate()
        
        time.sleep(1)
        
        # 测试路演报名重复提交
        test_roadshow_registration_duplicate()
        
        time.sleep(1)
        
        # 测试后端管理页面访问
        test_backend_list_access()
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
    
    print("\n" + "=" * 50)
    print("测试完成")
