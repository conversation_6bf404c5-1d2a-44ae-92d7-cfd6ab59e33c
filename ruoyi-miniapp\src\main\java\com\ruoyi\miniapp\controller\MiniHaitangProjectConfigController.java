package com.ruoyi.miniapp.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.miniapp.service.IMiniHaitangProjectConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import java.util.HashMap;
import java.util.Map;

/**
 * 天大海棠杯项目配置Controller
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@Api(tags = "天大海棠杯项目配置管理")
@RestController
@RequestMapping("/miniapp/haitang/project/config")
public class MiniHaitangProjectConfigController extends BaseController
{
    @Autowired
    private IMiniHaitangProjectConfigService miniHaitangProjectConfigService;

    /**
     * 获取赞助商图片
     */
    @ApiOperation("获取赞助商图片")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project:query')")
    @GetMapping("/sponsor")
    public AjaxResult getSponsorImage()
    {
        String sponsorUnit = miniHaitangProjectConfigService.getSponsorUnit();
        Map<String, Object> result = new HashMap<>();
        result.put("sponsorUnit", sponsorUnit);
        return AjaxResult.success(result);
    }

    /**
     * 更新赞助商图片
     */
    @ApiOperation("更新赞助商图片")
    @PreAuthorize("@ss.hasPermi('miniapp:haitang:project:edit')")
    @Log(title = "更新赞助商图片", businessType = BusinessType.UPDATE)
    @PutMapping("/sponsor")
    public AjaxResult updateSponsorImage(@RequestBody Map<String, String> params)
    {
        String sponsorUnit = params.get("sponsorUnit");
        boolean result = miniHaitangProjectConfigService.updateSponsorUnit(sponsorUnit);
        return result ? AjaxResult.success("更新成功") : AjaxResult.error("更新失败");
    }

    /**
     * 小程序端获取赞助商图片
     */
    @ApiOperation("小程序端获取赞助商图片")
    @GetMapping("/app/sponsor")
    public AjaxResult getSponsorImageForApp()
    {
        String sponsorUnit = miniHaitangProjectConfigService.getSponsorUnit();
        Map<String, Object> result = new HashMap<>();
        result.put("sponsorUnit", sponsorUnit);
        return AjaxResult.success(result);
    }
}
