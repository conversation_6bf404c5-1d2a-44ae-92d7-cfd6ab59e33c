{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue?vue&type=style&index=0&id=265cb072&prod&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue", "mtime": 1754384002001}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751437914525}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751437916167}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751437915128}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5mb3JtLWNvbmZpZy1jb250YWluZXIgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5jb25maWctaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiAyMHB4IDI0cHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQouY29uZmlnLWhlYWRlciBoMyB7DQogIG1hcmdpbjogMDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmNvbmZpZy1jb250ZW50IHsNCiAgcGFkZGluZzogMjRweDsNCiAgbWluLWhlaWdodDogMjAwcHg7DQp9DQoNCi5lbmFibGVkLWNvbmZpZyB7DQogIGJvcmRlcjogMXB4IHNvbGlkICM2N2MyM2E7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgcGFkZGluZzogMTZweDsNCiAgYmFja2dyb3VuZDogI2YwZjlmZjsNCn0NCg0KLmVuYWJsZWQtaGVhZGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KfQ0KDQouZW5hYmxlZC1oZWFkZXIgaDQgew0KICBtYXJnaW46IDA7DQogIGNvbG9yOiAjNjdjMjNhOw0KICBmb250LXNpemU6IDE2cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogOHB4Ow0KfQ0KDQouZW5hYmxlZC1kZXNjcmlwdGlvbiB7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIGNvbG9yOiAjNjA2MjY2Ow0KfQ0KDQouZm9ybS1wcmV2aWV3IGg1IHsNCiAgbWFyZ2luOiAwIDAgMTJweCAwOw0KICBjb2xvcjogIzMwMzEzMzsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQouZmllbGQtbGlzdCB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCn0NCg0KLmZpZWxkLWl0ZW0gew0KICBwYWRkaW5nOiAxMnB4IDE2cHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOw0KICBiYWNrZ3JvdW5kOiAjZmFmYmZjOw0KfQ0KDQouZmllbGQtaXRlbTpsYXN0LWNoaWxkIHsNCiAgYm9yZGVyLWJvdHRvbTogbm9uZTsNCn0NCg0KLmZpZWxkLWluZm8gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDEycHg7DQp9DQoNCi5maWVsZC1pY29uIHsNCiAgY29sb3I6ICM0MDllZmY7DQogIGZvbnQtc2l6ZTogMTZweDsNCn0NCg0KLmZpZWxkLWxhYmVsIHsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgY29sb3I6ICMzMDMxMzM7DQp9DQoNCi5maWVsZC10eXBlIHsNCiAgY29sb3I6ICM5MDkzOTk7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCg0KLmVtcHR5LWZvcm0sIC5uby1lbmFibGVkLWNvbmZpZyB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgcGFkZGluZzogNDBweCAyMHB4Ow0KICBjb2xvcjogIzkwOTM5OTsNCn0NCg0KLmVtcHR5LWZvcm0gaSwgLm5vLWVuYWJsZWQtY29uZmlnIGkgew0KICBmb250LXNpemU6IDQ4cHg7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQogIGRpc3BsYXk6IGJsb2NrOw0KfQ0KDQouY29uZmlnLWxpc3QtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQoubGlzdC1oZWFkZXIgew0KICBwYWRkaW5nOiAyMHB4IDI0cHg7DQogIGJhY2tncm91bmQ6ICNmOGY5ZmE7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOw0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQp9DQoNCi5saXN0LWhlYWRlciBoNCB7DQogIG1hcmdpbjogMDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmZvcm0tZmllbGRzLWNvbmZpZyB7DQogIG1heC1oZWlnaHQ6IDYwMHB4Ow0KICBvdmVyZmxvdy15OiBhdXRvOw0KfQ0KDQouZm9ybS1maWVsZHMtdG9vbGJhciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogMTZweDsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7DQp9DQoNCi50b29sYmFyLWxlZnQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBnYXA6IDEycHg7DQp9DQoNCi5mb3JtLWZpZWxkcy1saXN0IHsNCiAgcGFkZGluZzogMTZweDsNCn0NCg0KLmVtcHR5LWZpZWxkcyB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgcGFkZGluZzogNDBweCAyMHB4Ow0KICBjb2xvcjogIzkwOTM5OTsNCn0NCg0KLmVtcHR5LWZpZWxkcyBpIHsNCiAgZm9udC1zaXplOiA0OHB4Ow0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KICBkaXNwbGF5OiBibG9jazsNCn0NCg0KLmZpZWxkLWNvbmZpZy1pdGVtIHsNCiAgYm9yZGVyOiAxcHggc29saWQgI2U0ZTdlZDsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBtYXJnaW4tYm90dG9tOiAxMnB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQouZmllbGQtY29uZmlnLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMTJweDsNCiAgcGFkZGluZzogMTJweCAxNnB4Ow0KICBiYWNrZ3JvdW5kOiAjZmFmYmZjOw0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsNCn0NCg0KLmZpZWxkLWluZGV4IHsNCiAgZGlzcGxheTogaW5saW5lLWZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICB3aWR0aDogMjRweDsNCiAgaGVpZ2h0OiAyNHB4Ow0KICBiYWNrZ3JvdW5kOiAjNDA5ZWZmOw0KICBjb2xvcjogd2hpdGU7DQogIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCn0NCg0KLmZpZWxkLWNvbmZpZy1ib2R5IHsNCiAgcGFkZGluZzogMTJweCAxNnB4Ow0KfQ0KDQoucHJldmlldy1oZWFkZXIgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDI0cHg7DQogIHBhZGRpbmctYm90dG9tOiAxNnB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U5ZWNlZjsNCn0NCg0KLnByZXZpZXctaGVhZGVyIGgzIHsNCiAgbWFyZ2luOiAwIDAgOHB4IDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KICBmb250LXNpemU6IDE4cHg7DQp9DQoNCi5wcmV2aWV3LWhlYWRlciBwIHsNCiAgbWFyZ2luOiAwOw0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQoucHJldmlldy1maWVsZCB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5wcmV2aWV3LWxhYmVsIHsNCiAgZGlzcGxheTogYmxvY2s7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCiAgY29sb3I6ICMzMDMxMzM7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi5yZXF1aXJlZCB7DQogIGNvbG9yOiAjZjU2YzZjOw0KICBtYXJnaW4tbGVmdDogNHB4Ow0KfQ0KDQoucHJldmlldy1pbnB1dCB7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQovKiDmu5rliqjljZXpgInphY3nva7moLflvI8gKi8NCi5waWNrZXItb3B0aW9ucy1jb25maWcgew0KICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIHBhZGRpbmc6IDEycHg7DQogIGJhY2tncm91bmQ6ICNmYWZiZmM7DQp9DQoNCi5waWNrZXItb3B0aW9ucy1oZWFkZXIgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIG1hcmdpbi1ib3R0b206IDEycHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQoucGlja2VyLW9wdGlvbnMtbGlzdCB7DQogIG1heC1oZWlnaHQ6IDIwMHB4Ow0KICBvdmVyZmxvdy15OiBhdXRvOw0KfQ0KDQoucGlja2VyLW9wdGlvbi1pdGVtIHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiA4cHg7DQogIG1hcmdpbi1ib3R0b206IDhweDsNCiAgcGFkZGluZzogOHB4Ow0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi5waWNrZXItb3B0aW9uLWl0ZW06bGFzdC1jaGlsZCB7DQogIG1hcmdpbi1ib3R0b206IDA7DQp9DQoNCi5lbXB0eS1waWNrZXItb3B0aW9ucyB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgcGFkZGluZzogMjBweDsNCiAgY29sb3I6ICM5MDkzOTk7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLyog5rua5Yqo5Y2V6YCJ6aKE6KeI5qC35byPICovDQoucGlja2VyLXByZXZpZXcgew0KICBib3JkZXI6IDFweCBzb2xpZCAjZTRlN2VkOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5waWNrZXItZGlzcGxheSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogOHB4IDEycHg7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOw0KICBjdXJzb3I6IG5vdC1hbGxvd2VkOw0KfQ0KDQoucGlja2VyLXBsYWNlaG9sZGVyIHsNCiAgY29sb3I6ICNjMGM0Y2M7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLnBpY2tlci1vcHRpb25zLXByZXZpZXcgew0KICBtYXgtaGVpZ2h0OiAxMjBweDsNCiAgb3ZlcmZsb3cteTogYXV0bzsNCiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCn0NCg0KLnBpY2tlci1vcHRpb24tcHJldmlldyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogOHB4IDEycHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllY2VmOw0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjMzAzMTMzOw0KfQ0KDQoucGlja2VyLW9wdGlvbi1wcmV2aWV3Omxhc3QtY2hpbGQgew0KICBib3JkZXItYm90dG9tOiBub25lOw0KfQ0KDQoucGlja2VyLW9wdGlvbi1wcmV2aWV3LmRpc2FibGVkIHsNCiAgY29sb3I6ICNjMGM0Y2M7DQogIGJhY2tncm91bmQ6ICNmNWY3ZmE7DQp9DQoNCi5kaXNhYmxlZC10YWcgew0KICBmb250LXNpemU6IDEycHg7DQogIGNvbG9yOiAjZjU2YzZjOw0KICBiYWNrZ3JvdW5kOiAjZmVmMGYwOw0KICBwYWRkaW5nOiAycHggNnB4Ow0KICBib3JkZXItcmFkaXVzOiAycHg7DQp9DQo="}, null]}