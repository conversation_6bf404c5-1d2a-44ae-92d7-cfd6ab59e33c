{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue", "mtime": 1754384002001}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Rm9ybUNvbmZpZywgZ2V0Rm9ybUNvbmZpZywgZGVsRm9ybUNvbmZpZywgYWRkRm9ybUNvbmZpZywgdXBkYXRlRm9ybUNvbmZpZywgZW5hYmxlRm9ybUNvbmZpZywgZ2V0U3BvbnNvckltYWdlLCB1cGRhdGVTcG9uc29ySW1hZ2UgfSBmcm9tICJAL2FwaS9taW5pYXBwL2hhaXRhbmcvZm9ybUNvbmZpZyI7DQppbXBvcnQgcmVxdWVzdCBmcm9tICdAL3V0aWxzL3JlcXVlc3QnOw0KaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJGb3JtQ29uZmlnIiwNCiAgZGljdHM6IFsnc3lzX25vcm1hbF9kaXNhYmxlJ10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIGxpc3RMb2FkaW5nOiBmYWxzZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva7ooajmoLzmlbDmja4NCiAgICAgIGZvcm1Db25maWdMaXN0OiBbXSwNCiAgICAgIC8vIOW9k+WJjeWQr+eUqOeahOmFjee9rg0KICAgICAgZW5hYmxlZENvbmZpZzogbnVsbCwNCiAgICAgIC8vIOWQr+eUqOmFjee9rueahOihqOWNleWtl+autQ0KICAgICAgZW5hYmxlZEZvcm1GaWVsZHM6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5piv5ZCm5pi+56S66KGo5Y2V6YWN572u5by55Ye65bGCDQogICAgICBmb3JtQ29uZmlnT3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLrooajljZXpooTop4jlvLnlh7rlsYINCiAgICAgIHByZXZpZXdEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIC8vIOW9k+WJjemFjee9rueahOihqOWNleWtl+autQ0KICAgICAgY3VycmVudEZvcm1GaWVsZHM6IFtdLA0KICAgICAgLy8g5b2T5YmN5pON5L2c55qE6YWN572uDQogICAgICBjdXJyZW50Q29uZmlnOiBudWxsLA0KICAgICAgLy8g5rua5Yqo5Y2V6YCJ5a2X5q6155qE57yW6L6R5pWw5o2u57yT5a2YDQogICAgICBwaWNrZXJFZGl0RGF0YToge30sDQogICAgICAvLyDotZ7liqnllYblm77niYfnm7jlhbMNCiAgICAgIHNwb25zb3JEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRTcG9uc29ySW1hZ2U6ICcnLA0KICAgICAgdXBsb2FkQWN0aW9uOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9jb21tb24vdXBsb2FkIiwNCiAgICAgIHVwbG9hZEhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKSB9LA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGNvbmZpZ05hbWU6IG51bGwsDQogICAgICAgIHN0YXR1czogbnVsbA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGNvbmZpZ05hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6YWN572u5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5sb2FkRW5hYmxlZENvbmZpZygpOw0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOWKoOi9veWQr+eUqOeahOmFjee9riAqLw0KICAgIGxvYWRFbmFibGVkQ29uZmlnKCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIHJlcXVlc3Qoew0KICAgICAgICB1cmw6ICcvbWluaWFwcC9oYWl0YW5nL2Zvcm1Db25maWcvZW5hYmxlZCcsDQogICAgICAgIG1ldGhvZDogJ2dldCcNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIHRoaXMuZW5hYmxlZENvbmZpZyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgdGhpcy5sb2FkRW5hYmxlZEZvcm1GaWVsZHMoKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmVuYWJsZWRDb25maWcgPSBudWxsOw0KICAgICAgICAgIHRoaXMuZW5hYmxlZEZvcm1GaWVsZHMgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy5lbmFibGVkQ29uZmlnID0gbnVsbDsNCiAgICAgICAgdGhpcy5lbmFibGVkRm9ybUZpZWxkcyA9IFtdOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWKoOi9veWQr+eUqOmFjee9rueahOihqOWNleWtl+autSAqLw0KICAgIGxvYWRFbmFibGVkRm9ybUZpZWxkcygpIHsNCiAgICAgIGlmICh0aGlzLmVuYWJsZWRDb25maWcgJiYgdGhpcy5lbmFibGVkQ29uZmlnLmZvcm1Db25maWcpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmVuYWJsZWRGb3JtRmllbGRzID0gSlNPTi5wYXJzZSh0aGlzLmVuYWJsZWRDb25maWcuZm9ybUNvbmZpZyk7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmVuYWJsZWRGb3JtRmllbGRzID0gW107DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZW5hYmxlZEZvcm1GaWVsZHMgPSBbXTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmn6Xor6LlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva7liJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0Rm9ybUNvbmZpZyh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtQ29uZmlnTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBjb25maWdJZDogbnVsbCwNCiAgICAgICAgY29uZmlnTmFtZTogbnVsbCwNCiAgICAgICAgY29uZmlnRGVzY3JpcHRpb246IG51bGwsDQogICAgICAgIGZvcm1Db25maWc6IG51bGwsDQogICAgICAgIGlzRW5hYmxlZDogIjAiLA0KICAgICAgICBzb3J0T3JkZXI6IDAsDQogICAgICAgIHN0YXR1czogIjAiLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIHJlbWFyazogbnVsbA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uY29uZmlnSWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva4iOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IGNvbmZpZ0lkID0gcm93LmNvbmZpZ0lkIHx8IHRoaXMuaWRzDQogICAgICBnZXRGb3JtQ29uZmlnKGNvbmZpZ0lkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva4iOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uY29uZmlnSWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlRm9ybUNvbmZpZyh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZEZvcm1Db25maWcodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgY29uZmlnSWRzID0gcm93LmNvbmZpZ0lkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5aSp5aSn5rW35qOg5p2v6aG555uu5oql5ZCN6KGo5Y2V6YWN572u57yW5Y+35Li6IicgKyBjb25maWdJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxGb3JtQ29uZmlnKGNvbmZpZ0lkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyoqIOihqOWNlemFjee9ruaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUZvcm1Db25maWcocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRDb25maWcgPSByb3c7DQogICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gW107DQogICAgICB0aGlzLnBpY2tlckVkaXREYXRhID0ge307IC8vIOa4heepuue8lui+keaVsOaNrue8k+WtmA0KDQogICAgICBpZiAocm93LmZvcm1Db25maWcpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gSlNPTi5wYXJzZShyb3cuZm9ybUNvbmZpZyk7DQogICAgICAgICAgLy8g5Yid5aeL5YyW5rua5Yqo5Y2V6YCJ5a2X5q6155qE57yW6L6R5pWw5o2uDQogICAgICAgICAgdGhpcy5jdXJyZW50Rm9ybUZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgIGlmIChmaWVsZC50eXBlID09PSAncGlja2VyJyAmJiBmaWVsZC5vcHRpb25zKSB7DQogICAgICAgICAgICAgIGNvbnN0IGZpZWxkS2V5ID0gZmllbGQubmFtZSB8fCAndGVtcF8nICsgRGF0ZS5ub3coKTsNCiAgICAgICAgICAgICAgdGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV0gPSB0aGlzLmdldFBpY2tlck9wdGlvbnMoZmllbGQub3B0aW9ucyk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gW107DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybUNvbmZpZ09wZW4gPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOmihOiniOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVByZXZpZXcocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRDb25maWcgPSByb3c7DQogICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gW107DQogICAgICBpZiAocm93LmZvcm1Db25maWcpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gSlNPTi5wYXJzZShyb3cuZm9ybUNvbmZpZyk7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gW107DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMucHJldmlld0RpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOWQr+eUqOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUVuYWJsZShyb3cpIHsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+WQr+eUqOatpOmFjee9ruWwhuemgeeUqOWFtuS7luaJgOaciemFjee9ru+8jOaYr+WQpuehruiupOWQr+eUqO+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBlbmFibGVGb3JtQ29uZmlnKHJvdy5jb25maWdJZCk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5sb2FkRW5hYmxlZENvbmZpZygpOw0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5ZCv55So5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5re75Yqg6KGo5Y2V5a2X5q61ICovDQogICAgYWRkRm9ybUZpZWxkKCkgew0KICAgICAgY29uc3QgZGVmYXVsdE5hbWUgPSB0aGlzLmdlbmVyYXRlVW5pcXVlRmllbGROYW1lKCdmaWVsZCcpOw0KICAgICAgY29uc3QgbmV3RmllbGQgPSB7DQogICAgICAgIG5hbWU6IGRlZmF1bHROYW1lLA0KICAgICAgICBsYWJlbDogJycsDQogICAgICAgIHR5cGU6ICdpbnB1dCcsDQogICAgICAgIHJlcXVpcmVkOiBmYWxzZSwNCiAgICAgICAgb3B0aW9uczogJycNCiAgICAgIH07DQogICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzLnB1c2gobmV3RmllbGQpOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOihqOWNleWtl+autSAqLw0KICAgIHJlbW92ZUZvcm1GaWVsZChpbmRleCkgew0KICAgICAgdGhpcy5jdXJyZW50Rm9ybUZpZWxkcy5zcGxpY2UoaW5kZXgsIDEpOw0KICAgIH0sDQogICAgLyoqIOeUn+aIkOWUr+S4gOWtl+auteWQjSAqLw0KICAgIGdlbmVyYXRlVW5pcXVlRmllbGROYW1lKHByZWZpeCkgew0KICAgICAgbGV0IGNvdW50ZXIgPSAxOw0KICAgICAgbGV0IG5hbWUgPSBwcmVmaXggKyBjb3VudGVyOw0KICAgICAgd2hpbGUgKHRoaXMuY3VycmVudEZvcm1GaWVsZHMuc29tZShmaWVsZCA9PiBmaWVsZC5uYW1lID09PSBuYW1lKSkgew0KICAgICAgICBjb3VudGVyKys7DQogICAgICAgIG5hbWUgPSBwcmVmaXggKyBjb3VudGVyOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIG5hbWU7DQogICAgfSwNCiAgICAvKiog5Yik5pat5a2X5q6157G75Z6L5piv5ZCm6ZyA6KaB6YCJ6aG5ICovDQogICAgbmVlZE9wdGlvbnModHlwZSkgew0KICAgICAgcmV0dXJuIFsncmFkaW8nLCAnY2hlY2tib3gnLCAnc2VsZWN0JywgJ3JhZGlvX290aGVyJywgJ2NoZWNrYm94X290aGVyJywgJ3NlbGVjdF9vdGhlcicsICdwaWNrZXInXS5pbmNsdWRlcyh0eXBlKTsNCiAgICB9LA0KICAgIC8qKiDlpITnkIbmqKHmnb/lkb3ku6QgKi8NCiAgICBoYW5kbGVUZW1wbGF0ZUNvbW1hbmQoY29tbWFuZCkgew0KICAgICAgaWYgKGNvbW1hbmQgPT09ICdjbGVhcicpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu6K6k5riF56m65omA5pyJ5a2X5q6177yfJykudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5jdXJyZW50Rm9ybUZpZWxkcyA9IFtdOw0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCB0ZW1wbGF0ZXMgPSB7DQogICAgICAgIGJhc2ljOiBbDQogICAgICAgICAgeyBsYWJlbDogJ+Wnk+WQjScsIG5hbWU6ICcnLCB0eXBlOiAnaW5wdXQnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn6IGU57O755S16K+dJywgbmFtZTogJycsIHR5cGU6ICd0ZWwnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn6YKu566x5Zyw5Z2AJywgbmFtZTogJycsIHR5cGU6ICdlbWFpbCcsIHJlcXVpcmVkOiBmYWxzZSwgb3B0aW9uczogJycgfQ0KICAgICAgICBdLA0KICAgICAgICBwcm9qZWN0OiBbDQogICAgICAgICAgeyBsYWJlbDogJ+mhueebruWQjeensCcsIG5hbWU6ICcnLCB0eXBlOiAnaW5wdXQnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5Zui6Zif6LSf6LSj5Lq6JywgbmFtZTogJycsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfogZTns7vnlLXor50nLCBuYW1lOiAnJywgdHlwZTogJ3RlbCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfpgq7nrrHlnLDlnYAnLCBuYW1lOiAnJywgdHlwZTogJ2VtYWlsJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+mhueebrueugOS7iycsIG5hbWU6ICcnLCB0eXBlOiAndGV4dGFyZWEnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn6aG555uu57G75Z6LJywgbmFtZTogJycsIHR5cGU6ICdzZWxlY3Rfb3RoZXInLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJ+enkeaKgOWIm+aWsCzllYbkuJrmqKHlvI8s56S+5Lya5YWs55uKLOaWh+WMluWIm+aEjycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5Zui6Zif6KeE5qihJywgbmFtZTogJycsIHR5cGU6ICdyYWRpbycsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnMeS6uiwyLTPkurosNC015Lq6LDYtMTDkurosMTDkurrku6XkuIonIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+mhueebrumYtuautScsIG5hbWU6ICcnLCB0eXBlOiAncGlja2VyJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICdbeyJ0ZXh0Ijoi5Yib5oSP6Zi25q61IiwiZGlzYWJsZWQiOmZhbHNlfSx7InRleHQiOiLliJ3liJvpmLbmrrUiLCJkaXNhYmxlZCI6ZmFsc2V9LHsidGV4dCI6IuaIkOmVv+mYtuautSIsImRpc2FibGVkIjpmYWxzZX0seyJ0ZXh0Ijoi5oiQ54af6Zi25q61IiwiZGlzYWJsZWQiOnRydWV9XScgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5oql5ZCN5pel5pyfJywgbmFtZTogJycsIHR5cGU6ICdkYXRlJywgcmVxdWlyZWQ6IGZhbHNlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67orqHliJLkuaYnLCBuYW1lOiAnJywgdHlwZTogJ2ZpbGUnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5ryU56S66KeG6aKRJywgbmFtZTogJycsIHR5cGU6ICdmaWxlJywgcmVxdWlyZWQ6IGZhbHNlLCBvcHRpb25zOiAnJyB9DQogICAgICAgIF0NCiAgICAgIH07DQoNCiAgICAgIGlmICh0ZW1wbGF0ZXNbY29tbWFuZF0pIHsNCiAgICAgICAgdGhpcy5jdXJyZW50Rm9ybUZpZWxkcyA9IHRlbXBsYXRlc1tjb21tYW5kXS5tYXAoZmllbGQgPT4gKHsNCiAgICAgICAgICAuLi5maWVsZCwNCiAgICAgICAgICBuYW1lOiB0aGlzLmdlbmVyYXRlVW5pcXVlRmllbGROYW1lKGZpZWxkLmxhYmVsLnRvTG93ZXJDYXNlKCkpDQogICAgICAgIH0pKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDpooTop4jooajljZUgKi8NCiAgICBoYW5kbGVQcmV2aWV3Rm9ybSgpIHsNCiAgICAgIHRoaXMucHJldmlld0RpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOS/neWtmOihqOWNlemFjee9riAqLw0KICAgIHNhdmVGb3JtQ29uZmlnKCkgew0KICAgICAgaWYgKCF0aGlzLmN1cnJlbnRDb25maWcpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+WFiOmAieaLqeimgemFjee9rueahOihqOWNlSIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOmqjOivgeWtl+autemFjee9rg0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmN1cnJlbnRGb3JtRmllbGRzLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IGZpZWxkID0gdGhpcy5jdXJyZW50Rm9ybUZpZWxkc1tpXTsNCiAgICAgICAgaWYgKCFmaWVsZC5sYWJlbCkgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKGDnrKwke2kgKyAxfeS4quWtl+auteeahOagh+etvuS4jeiDveS4uuepumApOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoIWZpZWxkLm5hbWUpIHsNCiAgICAgICAgICBmaWVsZC5uYW1lID0gdGhpcy5nZW5lcmF0ZVVuaXF1ZUZpZWxkTmFtZShmaWVsZC5sYWJlbC50b0xvd2VyQ2FzZSgpKTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5uZWVkT3B0aW9ucyhmaWVsZC50eXBlKSAmJiAhZmllbGQub3B0aW9ucykgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKGDnrKwke2kgKyAxfeS4quWtl+autSIke2ZpZWxkLmxhYmVsfSLpnIDopoHorr7nva7pgInpobnlhoXlrrlgKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgY29uc3QgZm9ybURhdGEgPSB7DQogICAgICAgIGNvbmZpZ0lkOiB0aGlzLmN1cnJlbnRDb25maWcuY29uZmlnSWQsDQogICAgICAgIGZvcm1Db25maWc6IEpTT04uc3RyaW5naWZ5KHRoaXMuY3VycmVudEZvcm1GaWVsZHMpDQogICAgICB9Ow0KDQogICAgICB1cGRhdGVGb3JtQ29uZmlnKGZvcm1EYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6KGo5Y2V6YWN572u5L+d5a2Y5oiQ5YqfIik7DQogICAgICAgIHRoaXMuZm9ybUNvbmZpZ09wZW4gPSBmYWxzZTsNCiAgICAgICAgdGhpcy5sb2FkRW5hYmxlZENvbmZpZygpOw0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOiOt+WPluWtl+autemAiemhuSAqLw0KICAgIGdldEZpZWxkT3B0aW9ucyhvcHRpb25zKSB7DQogICAgICBpZiAoIW9wdGlvbnMpIHJldHVybiBbXTsNCiAgICAgIHJldHVybiBvcHRpb25zLnNwbGl0KCcsJykubWFwKG9wdCA9PiBvcHQudHJpbSgpKS5maWx0ZXIob3B0ID0+IG9wdCk7DQogICAgfSwNCiAgICAvKiog6I635Y+W5rua5Yqo5Y2V6YCJ6YCJ6aG5ICovDQogICAgZ2V0UGlja2VyT3B0aW9ucyhvcHRpb25zKSB7DQogICAgICBpZiAoIW9wdGlvbnMpIHJldHVybiBbXTsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2Uob3B0aW9ucyk7DQogICAgICAgIC8vIOehruS/nei/lOWbnueahOaYr+aVsOe7hOagvOW8jw0KICAgICAgICByZXR1cm4gQXJyYXkuaXNBcnJheShwYXJzZWQpID8gcGFyc2VkIDogW107DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIC8vIOWmguaenOino+aekOWksei0pe+8jOWwneivleaMiemAl+WPt+WIhumalOeahOagvOW8j+WkhOeQhg0KICAgICAgICBpZiAodHlwZW9mIG9wdGlvbnMgPT09ICdzdHJpbmcnICYmIG9wdGlvbnMudHJpbSgpKSB7DQogICAgICAgICAgcmV0dXJuIG9wdGlvbnMuc3BsaXQoJywnKS5tYXAob3B0ID0+ICh7DQogICAgICAgICAgICB0ZXh0OiBvcHQudHJpbSgpLA0KICAgICAgICAgICAgZGlzYWJsZWQ6IGZhbHNlDQogICAgICAgICAgfSkpLmZpbHRlcihvcHQgPT4gb3B0LnRleHQpOw0KICAgICAgICB9DQogICAgICAgIHJldHVybiBbXTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmt7vliqDmu5rliqjljZXpgInpgInpobkgKi8NCiAgICBhZGRQaWNrZXJPcHRpb24oZmllbGQpIHsNCiAgICAgIGNvbnN0IGZpZWxkS2V5ID0gZmllbGQubmFtZSB8fCAndGVtcF8nICsgRGF0ZS5ub3coKTsNCiAgICAgIGlmICghdGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV0pIHsNCiAgICAgICAgdGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV0gPSB0aGlzLmdldFBpY2tlck9wdGlvbnMoZmllbGQub3B0aW9ucyk7DQogICAgICB9DQogICAgICB0aGlzLnBpY2tlckVkaXREYXRhW2ZpZWxkS2V5XS5wdXNoKHsNCiAgICAgICAgdGV4dDogJycsDQogICAgICAgIGRpc2FibGVkOiBmYWxzZQ0KICAgICAgfSk7DQogICAgICB0aGlzLnVwZGF0ZVBpY2tlck9wdGlvbnNGcm9tRWRpdChmaWVsZCk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5rua5Yqo5Y2V6YCJ6YCJ6aG5ICovDQogICAgcmVtb3ZlUGlja2VyT3B0aW9uKGZpZWxkLCBpbmRleCkgew0KICAgICAgY29uc3QgZmllbGRLZXkgPSBmaWVsZC5uYW1lIHx8ICd0ZW1wXycgKyBEYXRlLm5vdygpOw0KICAgICAgaWYgKHRoaXMucGlja2VyRWRpdERhdGFbZmllbGRLZXldKSB7DQogICAgICAgIHRoaXMucGlja2VyRWRpdERhdGFbZmllbGRLZXldLnNwbGljZShpbmRleCwgMSk7DQogICAgICAgIHRoaXMudXBkYXRlUGlja2VyT3B0aW9uc0Zyb21FZGl0KGZpZWxkKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDojrflj5bnlKjkuo7nvJbovpHnmoTmu5rliqjljZXpgInpgInpobkgKi8NCiAgICBnZXRQaWNrZXJPcHRpb25zRm9yRWRpdChmaWVsZCkgew0KICAgICAgY29uc3QgZmllbGRLZXkgPSBmaWVsZC5uYW1lIHx8ICd0ZW1wXycgKyBEYXRlLm5vdygpOw0KICAgICAgaWYgKCF0aGlzLnBpY2tlckVkaXREYXRhW2ZpZWxkS2V5XSkgew0KICAgICAgICAvLyDliJ3lp4vljJbnvJbovpHmlbDmja4NCiAgICAgICAgdGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV0gPSB0aGlzLmdldFBpY2tlck9wdGlvbnMoZmllbGQub3B0aW9ucyk7DQogICAgICB9DQogICAgICByZXR1cm4gdGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV07DQogICAgfSwNCiAgICAvKiog5LuO57yW6L6R5pWw5o2u5pu05paw5rua5Yqo5Y2V6YCJ6YCJ6aG5ICovDQogICAgdXBkYXRlUGlja2VyT3B0aW9uc0Zyb21FZGl0KGZpZWxkKSB7DQogICAgICBjb25zdCBmaWVsZEtleSA9IGZpZWxkLm5hbWUgfHwgJ3RlbXBfJyArIERhdGUubm93KCk7DQogICAgICBpZiAodGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV0pIHsNCiAgICAgICAgZmllbGQub3B0aW9ucyA9IEpTT04uc3RyaW5naWZ5KHRoaXMucGlja2VyRWRpdERhdGFbZmllbGRLZXldKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmm7TmlrDmu5rliqjljZXpgInpgInpobkgKi8NCiAgICB1cGRhdGVQaWNrZXJPcHRpb25zKGZpZWxkKSB7DQogICAgICAvLyDlu7bov5/mm7TmlrDvvIznoa7kv53mlbDmja7lt7Lnu4/lj5jmm7QNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgbGV0IHBpY2tlck9wdGlvbnMgPSB0aGlzLmdldFBpY2tlck9wdGlvbnMoZmllbGQub3B0aW9ucyk7DQogICAgICAgIGZpZWxkLm9wdGlvbnMgPSBKU09OLnN0cmluZ2lmeShwaWNrZXJPcHRpb25zKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWkhOeQhuWtl+auteexu+Wei+WPmOabtCAqLw0KICAgIGhhbmRsZUZpZWxkVHlwZUNoYW5nZShmaWVsZCkgew0KICAgICAgY29uc3QgZmllbGRLZXkgPSBmaWVsZC5uYW1lIHx8ICd0ZW1wXycgKyBEYXRlLm5vdygpOw0KDQogICAgICAvLyDlvZPliIfmjaLliLDmu5rliqjljZXpgInml7bvvIzlpoLmnpzmsqHmnInpgInpobnliJnliJ3lp4vljJYNCiAgICAgIGlmIChmaWVsZC50eXBlID09PSAncGlja2VyJykgew0KICAgICAgICBpZiAoIWZpZWxkLm9wdGlvbnMgfHwgZmllbGQub3B0aW9ucyA9PT0gJycpIHsNCiAgICAgICAgICBjb25zdCBkZWZhdWx0T3B0aW9ucyA9IFsNCiAgICAgICAgICAgIHsgdGV4dDogJ+mAiemhuTEnLCBkaXNhYmxlZDogZmFsc2UgfSwNCiAgICAgICAgICAgIHsgdGV4dDogJ+mAiemhuTInLCBkaXNhYmxlZDogZmFsc2UgfQ0KICAgICAgICAgIF07DQogICAgICAgICAgZmllbGQub3B0aW9ucyA9IEpTT04uc3RyaW5naWZ5KGRlZmF1bHRPcHRpb25zKTsNCiAgICAgICAgICB0aGlzLnBpY2tlckVkaXREYXRhW2ZpZWxkS2V5XSA9IGRlZmF1bHRPcHRpb25zOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOmHjeaWsOWIneWni+WMlue8lui+keaVsOaNrg0KICAgICAgICAgIHRoaXMucGlja2VyRWRpdERhdGFbZmllbGRLZXldID0gdGhpcy5nZXRQaWNrZXJPcHRpb25zKGZpZWxkLm9wdGlvbnMpOw0KICAgICAgICB9DQogICAgICB9DQogICAgICAvLyDlvZPliIfmjaLliLDlhbbku5bpnIDopoHpgInpobnnmoTlrZfmrrXnsbvlnovml7bvvIzlpoLmnpzmmK9KU09O5qC85byP5YiZ6L2s5o2i5Li66YCX5Y+35YiG6ZqUDQogICAgICBlbHNlIGlmICh0aGlzLm5lZWRPcHRpb25zKGZpZWxkLnR5cGUpICYmIGZpZWxkLnR5cGUgIT09ICdwaWNrZXInICYmIGZpZWxkLm9wdGlvbnMpIHsNCiAgICAgICAgLy8g5riF6Zmk57yW6L6R5pWw5o2u57yT5a2YDQogICAgICAgIGRlbGV0ZSB0aGlzLnBpY2tlckVkaXREYXRhW2ZpZWxkS2V5XTsNCg0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2UoZmllbGQub3B0aW9ucyk7DQogICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocGFyc2VkKSkgew0KICAgICAgICAgICAgZmllbGQub3B0aW9ucyA9IHBhcnNlZC5tYXAob3B0ID0+IG9wdC50ZXh0IHx8IG9wdC5sYWJlbCB8fCBvcHQpLmpvaW4oJywnKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAvLyDlpoLmnpzkuI3mmK9KU09O5qC85byP77yM5L+d5oyB5Y6f5qC3DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOa4hemZpOe8lui+keaVsOaNrue8k+WtmA0KICAgICAgICBkZWxldGUgdGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV07DQogICAgICB9DQogICAgfSwNCiAgICAvKiog6I635Y+W6YCJ6aG56L6T5YWl5qGG5Y2g5L2N56ymICovDQogICAgZ2V0T3B0aW9uc1BsYWNlaG9sZGVyKHR5cGUpIHsNCiAgICAgIGNvbnN0IHBsYWNlaG9sZGVycyA9IHsNCiAgICAgICAgcmFkaW86ICfpgInpobnlhoXlrrnvvIzlpJrkuKrpgInpobnnlKjpgJflj7fliIbpmpTvvIzlpoLvvJrpgInpobkxLOmAiemhuTIs6YCJ6aG5MycsDQogICAgICAgIGNoZWNrYm94OiAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqU77yM5aaC77ya6YCJ6aG5MSzpgInpobkyLOmAiemhuTMnLA0KICAgICAgICBzZWxlY3Q6ICfpgInpobnlhoXlrrnvvIzlpJrkuKrpgInpobnnlKjpgJflj7fliIbpmpTvvIzlpoLvvJrpgInpobkxLOmAiemhuTIs6YCJ6aG5MycsDQogICAgICAgIHJhZGlvX290aGVyOiAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqU77yM5aaC77ya6YCJ6aG5MSzpgInpobkyLOmAiemhuTPvvIjkvJroh6rliqjmt7vliqAi5YW25LuWIumAiemhue+8iScsDQogICAgICAgIGNoZWNrYm94X290aGVyOiAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqU77yM5aaC77ya6YCJ6aG5MSzpgInpobkyLOmAiemhuTPvvIjkvJroh6rliqjmt7vliqAi5YW25LuWIumAiemhue+8iScsDQogICAgICAgIHNlbGVjdF9vdGhlcjogJ+mAiemhueWGheWuue+8jOWkmuS4qumAiemhueeUqOmAl+WPt+WIhumalO+8jOWmgu+8mumAiemhuTEs6YCJ6aG5MizpgInpobkz77yI5Lya6Ieq5Yqo5re75YqgIuWFtuS7liLpgInpobnvvIknLA0KICAgICAgICBwaWNrZXI6ICfmu5rliqjljZXpgInpgInpobnvvIzmlK/mjIHnpoHnlKjlip/og73vvIzor7fkvb/nlKjkuIrmlrnnmoTpgInpobnphY3nva7lmajov5vooYzorr7nva4nDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHBsYWNlaG9sZGVyc1t0eXBlXSB8fCAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqUJzsNCiAgICB9LA0KICAgIC8qKiDojrflj5blrZfmrrXlm77moIcgKi8NCiAgICBnZXRGaWVsZEljb24odHlwZSkgew0KICAgICAgY29uc3QgaWNvbnMgPSB7DQogICAgICAgIGlucHV0OiAnZWwtaWNvbi1lZGl0JywNCiAgICAgICAgdGV4dGFyZWE6ICdlbC1pY29uLWRvY3VtZW50JywNCiAgICAgICAgbnVtYmVyOiAnZWwtaWNvbi1zLWRhdGEnLA0KICAgICAgICBlbWFpbDogJ2VsLWljb24tbWVzc2FnZScsDQogICAgICAgIHRlbDogJ2VsLWljb24tcGhvbmUnLA0KICAgICAgICByYWRpbzogJ2VsLWljb24tY2lyY2xlLWNoZWNrJywNCiAgICAgICAgY2hlY2tib3g6ICdlbC1pY29uLWNoZWNrJywNCiAgICAgICAgc2VsZWN0OiAnZWwtaWNvbi1hcnJvdy1kb3duJywNCiAgICAgICAgcmFkaW9fb3RoZXI6ICdlbC1pY29uLWNpcmNsZS1wbHVzLW91dGxpbmUnLA0KICAgICAgICBjaGVja2JveF9vdGhlcjogJ2VsLWljb24tY2lyY2xlLXBsdXMtb3V0bGluZScsDQogICAgICAgIHNlbGVjdF9vdGhlcjogJ2VsLWljb24tY2lyY2xlLXBsdXMtb3V0bGluZScsDQogICAgICAgIHBpY2tlcjogJ2VsLWljb24tc29ydCcsDQogICAgICAgIGRhdGU6ICdlbC1pY29uLWRhdGUnLA0KICAgICAgICBmaWxlOiAnZWwtaWNvbi11cGxvYWQnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIGljb25zW3R5cGVdIHx8ICdlbC1pY29uLWVkaXQnOw0KICAgIH0sDQogICAgLyoqIOiOt+WPluWtl+auteexu+Wei+WQjeensCAqLw0KICAgIGdldEZpZWxkVHlwZU5hbWUodHlwZSkgew0KICAgICAgY29uc3QgbmFtZXMgPSB7DQogICAgICAgIGlucHV0OiAn5paH5pys6L6T5YWlJywNCiAgICAgICAgdGV4dGFyZWE6ICflpJrooYzmlofmnKwnLA0KICAgICAgICBudW1iZXI6ICfmlbDlrZfovpPlhaUnLA0KICAgICAgICBlbWFpbDogJ+mCrueusScsDQogICAgICAgIHRlbDogJ+eUteivnScsDQogICAgICAgIHJhZGlvOiAn5Y2V6YCJJywNCiAgICAgICAgY2hlY2tib3g6ICflpJrpgIknLA0KICAgICAgICBzZWxlY3Q6ICfkuIvmi4npgInmi6knLA0KICAgICAgICByYWRpb19vdGhlcjogJ+WNlemAiSvlhbbku5YnLA0KICAgICAgICBjaGVja2JveF9vdGhlcjogJ+WkmumAiSvlhbbku5YnLA0KICAgICAgICBzZWxlY3Rfb3RoZXI6ICfkuIvmi4kr5YW25LuWJywNCiAgICAgICAgcGlja2VyOiAn5rua5Yqo5Y2V6YCJJywNCiAgICAgICAgZGF0ZTogJ+aXpeacnycsDQogICAgICAgIGZpbGU6ICfmlofku7bkuIrkvKAnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIG5hbWVzW3R5cGVdIHx8ICfmnKrnn6XnsbvlnosnOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ21pbmlhcHAvaGFpdGFuZy9mb3JtQ29uZmlnL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYGZvcm1Db25maWdfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICAvKiog6LWe5Yqp5ZWG5Zu+54mH5LiK5Lyg5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlU3BvbnNvclVwbG9hZCgpIHsNCiAgICAgIHRoaXMubG9hZFNwb25zb3JJbWFnZSgpOw0KICAgICAgdGhpcy5zcG9uc29yRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICAvKiog5Yqg6L296LWe5Yqp5ZWG5Zu+54mHICovDQogICAgbG9hZFNwb25zb3JJbWFnZSgpIHsNCiAgICAgIGdldFNwb25zb3JJbWFnZSgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLnNwb25zb3JVbml0KSB7DQogICAgICAgICAgdGhpcy5jdXJyZW50U3BvbnNvckltYWdlID0gcmVzcG9uc2UuZGF0YS5zcG9uc29yVW5pdDsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRTcG9uc29ySW1hZ2UgPSAnJzsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmN1cnJlbnRTcG9uc29ySW1hZ2UgPSAnJzsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOS4iuS8oOWJjeagoemqjCAqLw0KICAgIGJlZm9yZVNwb25zb3JVcGxvYWQoZmlsZSkgew0KICAgICAgY29uc3QgaXNJbWFnZSA9IGZpbGUudHlwZS5pbmRleE9mKCdpbWFnZS8nKSA9PT0gMDsNCiAgICAgIGNvbnN0IGlzTHQ1TSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgNTsNCg0KICAgICAgaWYgKCFpc0ltYWdlKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfkuIrkvKDmlofku7blj6rog73mmK/lm77niYfmoLzlvI8hJyk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCiAgICAgIGlmICghaXNMdDVNKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfkuIrkvKDlm77niYflpKflsI/kuI3og73otoXov4cgNU1CIScpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgICByZXR1cm4gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDkuIrkvKDmiJDlip/lm57osIMgKi8NCiAgICBoYW5kbGVTcG9uc29yVXBsb2FkU3VjY2VzcyhyZXNwb25zZSkgew0KICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICBjb25zdCBpbWFnZVVybCA9IHJlc3BvbnNlLnVybDsNCiAgICAgICAgdXBkYXRlU3BvbnNvckltYWdlKGltYWdlVXJsKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRTcG9uc29ySW1hZ2UgPSBpbWFnZVVybDsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfotZ7liqnllYblm77niYfkuIrkvKDmiJDlip8nKTsNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfkv53lrZjotZ7liqnllYblm77niYflpLHotKUnKTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5Zu+54mH5LiK5Lyg5aSx6LSl77yaJyArIHJlc3BvbnNlLm1zZyk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5LiK5Lyg5aSx6LSl5Zue6LCDICovDQogICAgaGFuZGxlU3BvbnNvclVwbG9hZEVycm9yKCkgew0KICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+WbvueJh+S4iuS8oOWksei0pe+8jOivt+mHjeivlScpOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOi1nuWKqeWVhuWbvueJhyAqLw0KICAgIGhhbmRsZURlbGV0ZVNwb25zb3IoKSB7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTliKDpmaTlvZPliY3otZ7liqnllYblm77niYfvvJ8nKS50aGVuKCgpID0+IHsNCiAgICAgICAgdXBkYXRlU3BvbnNvckltYWdlKCcnKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRTcG9uc29ySW1hZ2UgPSAnJzsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfotZ7liqnllYblm77niYfliKDpmaTmiJDlip8nKTsNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfliKDpmaTotZ7liqnllYblm77niYflpLHotKUnKTsNCiAgICAgICAgfSk7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6d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file": "index.vue", "sourceRoot": "src/views/miniapp/haitang/projectFormConfig", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 表单配置管理区域 -->\r\n    <div class=\"form-config-container\">\r\n      <div class=\"config-header\">\r\n        <h3>项目报名表单配置管理</h3>\r\n        <div class=\"header-actions\">\r\n          <el-button\r\n            type=\"warning\"\r\n            icon=\"el-icon-picture\"\r\n            @click=\"handleSponsorUpload\"\r\n            v-hasPermi=\"['miniapp:haitang:project:edit']\"\r\n          >赞助商图片</el-button>\r\n          <el-button\r\n            type=\"primary\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"handleAdd\"\r\n            v-hasPermi=\"['miniapp:haitang:formConfig:add']\"\r\n          >新增配置</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"config-content\" v-loading=\"loading\">\r\n        <!-- 当前启用的配置 -->\r\n        <div v-if=\"enabledConfig\" class=\"enabled-config\">\r\n          <div class=\"enabled-header\">\r\n            <h4>\r\n              <i class=\"el-icon-check\" style=\"color: #67c23a;\"></i>\r\n              当前启用配置：{{ enabledConfig.configName }}\r\n            </h4>\r\n            <div class=\"enabled-actions\">\r\n              <el-button\r\n                size=\"small\"\r\n                type=\"primary\"\r\n                icon=\"el-icon-setting\"\r\n                @click=\"handleFormConfig(enabledConfig)\"\r\n                v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\r\n              >配置表单</el-button>\r\n              <el-button\r\n                size=\"small\"\r\n                type=\"success\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handlePreview(enabledConfig)\"\r\n              >预览表单</el-button>\r\n            </div>\r\n          </div>\r\n          <div class=\"enabled-description\">\r\n            <p>{{ enabledConfig.configDescription || '暂无描述' }}</p>\r\n          </div>\r\n          <div v-if=\"enabledFormFields.length > 0\" class=\"form-preview\">\r\n            <h5>表单字段预览：</h5>\r\n            <div class=\"field-list\">\r\n              <div v-for=\"(field, index) in enabledFormFields\" :key=\"index\" class=\"field-item\">\r\n                <div class=\"field-info\">\r\n                  <i :class=\"getFieldIcon(field.type)\" class=\"field-icon\"></i>\r\n                  <span class=\"field-label\">{{ field.label }}</span>\r\n                  <el-tag size=\"mini\" type=\"success\">{{ field.name }}</el-tag>\r\n                  <el-tag v-if=\"field.required\" size=\"mini\" type=\"danger\">必填</el-tag>\r\n                  <el-tag v-else size=\"mini\" type=\"info\">选填</el-tag>\r\n                  <span class=\"field-type\">{{ getFieldTypeName(field.type) }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div v-else class=\"empty-form\">\r\n            <i class=\"el-icon-document-add\"></i>\r\n            <p>该配置暂未设置表单字段，点击\"配置表单\"开始设置</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 无启用配置时的提示 -->\r\n        <div v-else class=\"no-enabled-config\">\r\n          <i class=\"el-icon-warning-outline\"></i>\r\n          <p>暂无启用的表单配置，请先创建并启用一个配置</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 所有配置列表 -->\r\n    <div class=\"config-list-container\">\r\n      <div class=\"list-header\">\r\n        <h4>所有表单配置</h4>\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"80px\">\r\n          <el-form-item label=\"配置名称\" prop=\"configName\">\r\n            <el-input\r\n              v-model=\"queryParams.configName\"\r\n              placeholder=\"请输入配置名称\"\r\n              clearable\r\n              style=\"width: 200px;\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select v-model=\"queryParams.status\" placeholder=\"请选择状态\" clearable style=\"width: 120px;\">\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_normal_disable\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <el-table v-loading=\"listLoading\" :data=\"formConfigList\" @selection-change=\"handleSelectionChange\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column label=\"配置名称\" align=\"center\" prop=\"configName\" min-width=\"150\" />\r\n        <el-table-column label=\"配置描述\" align=\"center\" prop=\"configDescription\" show-overflow-tooltip min-width=\"200\" />\r\n        <el-table-column label=\"是否启用\" align=\"center\" prop=\"isEnabled\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.isEnabled === '1'\" type=\"success\">已启用</el-tag>\r\n            <el-tag v-else type=\"info\">未启用</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"160\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"200\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-setting\"\r\n              @click=\"handleFormConfig(scope.row)\"\r\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\r\n            >配置</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-edit\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\r\n            >编辑</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-check\"\r\n              @click=\"handleEnable(scope.row)\"\r\n              v-hasPermi=\"['miniapp:haitang:formConfig:edit']\"\r\n              v-if=\"scope.row.isEnabled !== '1'\"\r\n            >启用</el-button>\r\n            <el-button\r\n              size=\"mini\"\r\n              type=\"text\"\r\n              icon=\"el-icon-delete\"\r\n              @click=\"handleDelete(scope.row)\"\r\n              v-hasPermi=\"['miniapp:haitang:formConfig:remove']\"\r\n            >删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 添加或修改配置基本信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-form-item label=\"配置名称\" prop=\"configName\">\r\n          <el-input v-model=\"form.configName\" placeholder=\"请输入配置名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"配置描述\" prop=\"configDescription\">\r\n          <el-input v-model=\"form.configDescription\" type=\"textarea\" :rows=\"3\" placeholder=\"请输入配置描述\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"sortOrder\">\r\n          <el-input-number v-model=\"form.sortOrder\" controls-position=\"right\" :min=\"0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"status\">\r\n          <el-radio-group v-model=\"form.status\">\r\n            <el-radio\r\n              v-for=\"dict in dict.type.sys_normal_disable\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.value\"\r\n            >{{dict.label}}</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"2\" placeholder=\"请输入备注\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单配置对话框 -->\r\n    <el-dialog title=\"表单字段配置\" :visible.sync=\"formConfigOpen\" width=\"1000px\" append-to-body>\r\n      <div class=\"form-fields-config\">\r\n        <!-- 工具栏 -->\r\n        <div class=\"form-fields-toolbar\">\r\n          <div class=\"toolbar-left\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"addFormField\" icon=\"el-icon-plus\">\r\n              添加字段\r\n            </el-button>\r\n            <el-dropdown @command=\"handleTemplateCommand\" size=\"small\">\r\n              <el-button size=\"small\">\r\n                预设模板<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n              </el-button>\r\n              <el-dropdown-menu slot=\"dropdown\">\r\n                <el-dropdown-item command=\"basic\">基础信息模板</el-dropdown-item>\r\n                <el-dropdown-item command=\"project\">项目报名模板</el-dropdown-item>\r\n                <el-dropdown-item command=\"clear\">清空所有字段</el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </el-dropdown>\r\n          </div>\r\n          <div class=\"toolbar-right\">\r\n            <el-button size=\"small\" @click=\"handlePreviewForm\" icon=\"el-icon-view\">预览表单</el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 字段配置列表 -->\r\n        <div class=\"form-fields-list\">\r\n          <div v-if=\"currentFormFields.length === 0\" class=\"empty-fields\">\r\n            <i class=\"el-icon-document-add\"></i>\r\n            <p>暂无字段，点击\"添加字段\"开始配置</p>\r\n          </div>\r\n          <div v-else>\r\n            <div v-for=\"(field, index) in currentFormFields\" :key=\"index\" class=\"field-config-item\">\r\n              <div class=\"field-config-header\">\r\n                <span class=\"field-index\">{{ index + 1 }}</span>\r\n                <el-input v-model=\"field.label\" placeholder=\"字段标签\" size=\"small\" style=\"width: 150px;\" />\r\n                <el-input v-model=\"field.name\" placeholder=\"字段名称\" size=\"small\" style=\"width: 120px;\" />\r\n                <el-select v-model=\"field.type\" placeholder=\"字段类型\" size=\"small\" style=\"width: 140px;\" @change=\"handleFieldTypeChange(field)\">\r\n                  <el-option label=\"📝 文本输入\" value=\"input\" />\r\n                  <el-option label=\"📄 多行文本\" value=\"textarea\" />\r\n                  <el-option label=\"🔢 数字输入\" value=\"number\" />\r\n                  <el-option label=\"📧 邮箱\" value=\"email\" />\r\n                  <el-option label=\"📞 电话\" value=\"tel\" />\r\n                  <el-option label=\"🔘 单选\" value=\"radio\" />\r\n                  <el-option label=\"☑️ 多选\" value=\"checkbox\" />\r\n                  <el-option label=\"📋 下拉选择\" value=\"select\" />\r\n                  <el-option label=\"🔘➕ 单选+其他\" value=\"radio_other\" />\r\n                  <el-option label=\"☑️➕ 多选+其他\" value=\"checkbox_other\" />\r\n                  <el-option label=\"📋➕ 下拉+其他\" value=\"select_other\" />\r\n                  <el-option label=\"🎡 滚动单选\" value=\"picker\" />\r\n                  <el-option label=\"📅 日期\" value=\"date\" />\r\n                  <el-option label=\"📎 文件上传\" value=\"file\" />\r\n                </el-select>\r\n                <el-checkbox v-model=\"field.required\" size=\"small\">必填</el-checkbox>\r\n                <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"removeFormField(index)\">删除</el-button>\r\n              </div>\r\n              <div class=\"field-config-body\" v-if=\"needOptions(field.type)\">\r\n                <!-- 滚动单选特殊配置 -->\r\n                <div v-if=\"field.type === 'picker'\" class=\"picker-options-config\">\r\n                  <div class=\"picker-options-header\">\r\n                    <span>选项配置（支持禁用选项）</span>\r\n                    <el-button size=\"mini\" type=\"primary\" @click=\"addPickerOption(field)\">添加选项</el-button>\r\n                  </div>\r\n                  <div class=\"picker-options-list\">\r\n                    <div v-for=\"(option, optIndex) in getPickerOptionsForEdit(field)\" :key=\"optIndex\" class=\"picker-option-item\">\r\n                      <el-input v-model=\"option.text\" placeholder=\"选项内容\" size=\"small\" style=\"width: 200px;\" @input=\"updatePickerOptionsFromEdit(field)\" />\r\n                      <el-checkbox v-model=\"option.disabled\" size=\"small\" @change=\"updatePickerOptionsFromEdit(field)\">禁用</el-checkbox>\r\n                      <el-button size=\"mini\" type=\"danger\" icon=\"el-icon-delete\" @click=\"removePickerOption(field, optIndex)\">删除</el-button>\r\n                    </div>\r\n                  </div>\r\n                  <div v-if=\"getPickerOptionsForEdit(field).length === 0\" class=\"empty-picker-options\">\r\n                    <span>暂无选项，点击\"添加选项\"开始配置</span>\r\n                  </div>\r\n                </div>\r\n                <!-- 其他字段类型的普通配置 -->\r\n                <el-input\r\n                  v-else\r\n                  v-model=\"field.options\"\r\n                  :placeholder=\"getOptionsPlaceholder(field.type)\"\r\n                  size=\"small\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"saveFormConfig\">保存配置</el-button>\r\n        <el-button @click=\"formConfigOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 表单预览对话框 -->\r\n    <el-dialog title=\"表单预览\" :visible.sync=\"previewDialogVisible\" width=\"600px\" append-to-body>\r\n      <div class=\"form-preview\">\r\n        <div class=\"preview-header\">\r\n          <h3>{{ currentConfig ? currentConfig.configName : '项目报名表' }}</h3>\r\n          <p>请填写以下信息完成报名</p>\r\n        </div>\r\n        <div class=\"preview-form\">\r\n          <div v-for=\"(field, index) in currentFormFields\" :key=\"index\" class=\"preview-field\">\r\n            <label class=\"preview-label\">\r\n              {{ field.label }}\r\n              <span v-if=\"field.required\" class=\"required\">*</span>\r\n            </label>\r\n            <div class=\"preview-input\">\r\n              <!-- 基础输入类型 -->\r\n              <el-input\r\n                v-if=\"['input', 'email', 'tel'].includes(field.type)\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <el-input\r\n                v-else-if=\"field.type === 'textarea'\"\r\n                type=\"textarea\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <!-- 数字类型 -->\r\n              <el-input-number\r\n                v-else-if=\"field.type === 'number'\"\r\n                :placeholder=\"'请输入' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <!-- 单选类型 -->\r\n              <el-radio-group v-else-if=\"field.type === 'radio'\" disabled>\r\n                <el-radio\r\n                  v-for=\"option in getFieldOptions(field.options)\"\r\n                  :key=\"option\"\r\n                  :label=\"option\"\r\n                >{{ option }}</el-radio>\r\n              </el-radio-group>\r\n              <!-- 单选+其他 -->\r\n              <div v-else-if=\"field.type === 'radio_other'\">\r\n                <el-radio-group disabled style=\"margin-bottom: 8px;\">\r\n                  <el-radio\r\n                    v-for=\"option in getFieldOptions(field.options)\"\r\n                    :key=\"option\"\r\n                    :label=\"option\"\r\n                  >{{ option }}</el-radio>\r\n                  <el-radio label=\"其他\">其他</el-radio>\r\n                </el-radio-group>\r\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\r\n              </div>\r\n              <!-- 多选类型 -->\r\n              <el-checkbox-group v-else-if=\"field.type === 'checkbox'\" disabled>\r\n                <el-checkbox\r\n                  v-for=\"option in getFieldOptions(field.options)\"\r\n                  :key=\"option\"\r\n                  :label=\"option\"\r\n                >{{ option }}</el-checkbox>\r\n              </el-checkbox-group>\r\n              <!-- 多选+其他 -->\r\n              <div v-else-if=\"field.type === 'checkbox_other'\">\r\n                <el-checkbox-group disabled style=\"margin-bottom: 8px;\">\r\n                  <el-checkbox\r\n                    v-for=\"option in getFieldOptions(field.options)\"\r\n                    :key=\"option\"\r\n                    :label=\"option\"\r\n                  >{{ option }}</el-checkbox>\r\n                  <el-checkbox label=\"其他\">其他</el-checkbox>\r\n                </el-checkbox-group>\r\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\r\n              </div>\r\n              <!-- 下拉选择 -->\r\n              <el-select v-else-if=\"field.type === 'select'\" :placeholder=\"'请选择' + field.label\" size=\"small\" disabled>\r\n                <el-option\r\n                  v-for=\"option in getFieldOptions(field.options)\"\r\n                  :key=\"option\"\r\n                  :label=\"option\"\r\n                  :value=\"option\"\r\n                />\r\n              </el-select>\r\n              <!-- 下拉+其他 -->\r\n              <div v-else-if=\"field.type === 'select_other'\">\r\n                <el-select :placeholder=\"'请选择' + field.label\" size=\"small\" disabled style=\"margin-bottom: 8px; width: 100%;\">\r\n                  <el-option\r\n                    v-for=\"option in getFieldOptions(field.options)\"\r\n                    :key=\"option\"\r\n                    :label=\"option\"\r\n                    :value=\"option\"\r\n                  />\r\n                  <el-option label=\"其他\" value=\"其他\" />\r\n                </el-select>\r\n                <el-input placeholder=\"选择'其他'时请在此输入具体内容\" size=\"small\" disabled />\r\n              </div>\r\n              <!-- 滚动单选 -->\r\n              <div v-else-if=\"field.type === 'picker'\" class=\"picker-preview\">\r\n                <div class=\"picker-display\">\r\n                  <span class=\"picker-placeholder\">{{ '请选择' + field.label }}</span>\r\n                  <i class=\"el-icon-arrow-down\"></i>\r\n                </div>\r\n                <div class=\"picker-options-preview\">\r\n                  <div v-for=\"(option, index) in getPickerOptions(field.options)\" :key=\"index\"\r\n                       :class=\"['picker-option-preview', { 'disabled': option.disabled }]\">\r\n                    {{ option.text }}\r\n                    <span v-if=\"option.disabled\" class=\"disabled-tag\">禁用</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- 日期类型 -->\r\n              <el-date-picker\r\n                v-else-if=\"field.type === 'date'\"\r\n                type=\"date\"\r\n                :placeholder=\"'请选择' + field.label\"\r\n                size=\"small\"\r\n                disabled\r\n              />\r\n              <!-- 文件上传 -->\r\n              <el-upload\r\n                v-else-if=\"field.type === 'file'\"\r\n                class=\"upload-demo\"\r\n                action=\"#\"\r\n                :auto-upload=\"false\"\r\n                disabled\r\n              >\r\n                <el-button size=\"small\" type=\"primary\" disabled>点击上传</el-button>\r\n              </el-upload>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"previewDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 赞助商图片上传对话框 -->\r\n    <el-dialog title=\"赞助商图片管理\" :visible.sync=\"sponsorDialogVisible\" width=\"600px\" append-to-body>\r\n      <div class=\"sponsor-upload-container\">\r\n        <div class=\"current-sponsor\" v-if=\"currentSponsorImage\">\r\n          <h4>当前赞助商图片</h4>\r\n          <div class=\"sponsor-image-preview\">\r\n            <img :src=\"currentSponsorImage\" alt=\"赞助商图片\" class=\"sponsor-img\" />\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"upload-section\">\r\n          <h4>{{ currentSponsorImage ? '更换赞助商图片' : '上传赞助商图片' }}</h4>\r\n          <el-upload\r\n            class=\"sponsor-uploader\"\r\n            :action=\"uploadAction\"\r\n            :headers=\"uploadHeaders\"\r\n            :show-file-list=\"false\"\r\n            :on-success=\"handleSponsorUploadSuccess\"\r\n            :on-error=\"handleSponsorUploadError\"\r\n            :before-upload=\"beforeSponsorUpload\"\r\n            accept=\"image/*\"\r\n          >\r\n            <div class=\"upload-area\">\r\n              <i class=\"el-icon-plus sponsor-uploader-icon\"></i>\r\n              <div class=\"upload-text\">点击上传图片</div>\r\n              <div class=\"upload-tip\">支持 JPG、PNG 格式，建议尺寸 400x200px</div>\r\n            </div>\r\n          </el-upload>\r\n        </div>\r\n\r\n        <div class=\"sponsor-actions\" v-if=\"currentSponsorImage\">\r\n          <el-button type=\"danger\" @click=\"handleDeleteSponsor\">删除当前图片</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"sponsorDialogVisible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listFormConfig, getFormConfig, delFormConfig, addFormConfig, updateFormConfig, enableFormConfig, getSponsorImage, updateSponsorImage } from \"@/api/miniapp/haitang/formConfig\";\r\nimport request from '@/utils/request';\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"FormConfig\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      listLoading: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 天大海棠杯项目报名表单配置表格数据\r\n      formConfigList: [],\r\n      // 当前启用的配置\r\n      enabledConfig: null,\r\n      // 启用配置的表单字段\r\n      enabledFormFields: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示表单配置弹出层\r\n      formConfigOpen: false,\r\n      // 是否显示表单预览弹出层\r\n      previewDialogVisible: false,\r\n      // 当前配置的表单字段\r\n      currentFormFields: [],\r\n      // 当前操作的配置\r\n      currentConfig: null,\r\n      // 滚动单选字段的编辑数据缓存\r\n      pickerEditData: {},\r\n      // 赞助商图片相关\r\n      sponsorDialogVisible: false,\r\n      currentSponsorImage: '',\r\n      uploadAction: process.env.VUE_APP_BASE_API + \"/common/upload\",\r\n      uploadHeaders: { Authorization: \"Bearer \" + getToken() },\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        configName: null,\r\n        status: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        configName: [\r\n          { required: true, message: \"配置名称不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.loadEnabledConfig();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 加载启用的配置 */\r\n    loadEnabledConfig() {\r\n      this.loading = true;\r\n      request({\r\n        url: '/miniapp/haitang/formConfig/enabled',\r\n        method: 'get'\r\n      }).then(response => {\r\n        if (response.data) {\r\n          this.enabledConfig = response.data;\r\n          this.loadEnabledFormFields();\r\n        } else {\r\n          this.enabledConfig = null;\r\n          this.enabledFormFields = [];\r\n        }\r\n        this.loading = false;\r\n      }).catch(() => {\r\n        this.enabledConfig = null;\r\n        this.enabledFormFields = [];\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 加载启用配置的表单字段 */\r\n    loadEnabledFormFields() {\r\n      if (this.enabledConfig && this.enabledConfig.formConfig) {\r\n        try {\r\n          this.enabledFormFields = JSON.parse(this.enabledConfig.formConfig);\r\n        } catch (e) {\r\n          this.enabledFormFields = [];\r\n        }\r\n      } else {\r\n        this.enabledFormFields = [];\r\n      }\r\n    },\r\n    /** 查询天大海棠杯项目报名表单配置列表 */\r\n    getList() {\r\n      this.listLoading = true;\r\n      listFormConfig(this.queryParams).then(response => {\r\n        this.formConfigList = response.rows;\r\n        this.total = response.total;\r\n        this.listLoading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        configId: null,\r\n        configName: null,\r\n        configDescription: null,\r\n        formConfig: null,\r\n        isEnabled: \"0\",\r\n        sortOrder: 0,\r\n        status: \"0\",\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.configId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加天大海棠杯项目报名表单配置\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const configId = row.configId || this.ids\r\n      getFormConfig(configId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改天大海棠杯项目报名表单配置\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.configId != null) {\r\n            updateFormConfig(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addFormConfig(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const configIds = row.configId || this.ids;\r\n      this.$modal.confirm('是否确认删除天大海棠杯项目报名表单配置编号为\"' + configIds + '\"的数据项？').then(function() {\r\n        return delFormConfig(configIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 表单配置按钮操作 */\r\n    handleFormConfig(row) {\r\n      this.currentConfig = row;\r\n      this.currentFormFields = [];\r\n      this.pickerEditData = {}; // 清空编辑数据缓存\r\n\r\n      if (row.formConfig) {\r\n        try {\r\n          this.currentFormFields = JSON.parse(row.formConfig);\r\n          // 初始化滚动单选字段的编辑数据\r\n          this.currentFormFields.forEach(field => {\r\n            if (field.type === 'picker' && field.options) {\r\n              const fieldKey = field.name || 'temp_' + Date.now();\r\n              this.pickerEditData[fieldKey] = this.getPickerOptions(field.options);\r\n            }\r\n          });\r\n        } catch (e) {\r\n          this.currentFormFields = [];\r\n        }\r\n      }\r\n      this.formConfigOpen = true;\r\n    },\r\n    /** 预览按钮操作 */\r\n    handlePreview(row) {\r\n      this.currentConfig = row;\r\n      this.currentFormFields = [];\r\n      if (row.formConfig) {\r\n        try {\r\n          this.currentFormFields = JSON.parse(row.formConfig);\r\n        } catch (e) {\r\n          this.currentFormFields = [];\r\n        }\r\n      }\r\n      this.previewDialogVisible = true;\r\n    },\r\n    /** 启用按钮操作 */\r\n    handleEnable(row) {\r\n      this.$modal.confirm('启用此配置将禁用其他所有配置，是否确认启用？').then(function() {\r\n        return enableFormConfig(row.configId);\r\n      }).then(() => {\r\n        this.loadEnabledConfig();\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"启用成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 添加表单字段 */\r\n    addFormField() {\r\n      const defaultName = this.generateUniqueFieldName('field');\r\n      const newField = {\r\n        name: defaultName,\r\n        label: '',\r\n        type: 'input',\r\n        required: false,\r\n        options: ''\r\n      };\r\n      this.currentFormFields.push(newField);\r\n    },\r\n    /** 删除表单字段 */\r\n    removeFormField(index) {\r\n      this.currentFormFields.splice(index, 1);\r\n    },\r\n    /** 生成唯一字段名 */\r\n    generateUniqueFieldName(prefix) {\r\n      let counter = 1;\r\n      let name = prefix + counter;\r\n      while (this.currentFormFields.some(field => field.name === name)) {\r\n        counter++;\r\n        name = prefix + counter;\r\n      }\r\n      return name;\r\n    },\r\n    /** 判断字段类型是否需要选项 */\r\n    needOptions(type) {\r\n      return ['radio', 'checkbox', 'select', 'radio_other', 'checkbox_other', 'select_other', 'picker'].includes(type);\r\n    },\r\n    /** 处理模板命令 */\r\n    handleTemplateCommand(command) {\r\n      if (command === 'clear') {\r\n        this.$modal.confirm('确认清空所有字段？').then(() => {\r\n          this.currentFormFields = [];\r\n        });\r\n        return;\r\n      }\r\n\r\n      const templates = {\r\n        basic: [\r\n          { label: '姓名', name: '', type: 'input', required: true, options: '' },\r\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\r\n          { label: '邮箱地址', name: '', type: 'email', required: false, options: '' }\r\n        ],\r\n        project: [\r\n          { label: '项目名称', name: '', type: 'input', required: true, options: '' },\r\n          { label: '团队负责人', name: '', type: 'input', required: true, options: '' },\r\n          { label: '联系电话', name: '', type: 'tel', required: true, options: '' },\r\n          { label: '邮箱地址', name: '', type: 'email', required: true, options: '' },\r\n          { label: '项目简介', name: '', type: 'textarea', required: true, options: '' },\r\n          { label: '项目类型', name: '', type: 'select_other', required: true, options: '科技创新,商业模式,社会公益,文化创意' },\r\n          { label: '团队规模', name: '', type: 'radio', required: true, options: '1人,2-3人,4-5人,6-10人,10人以上' },\r\n          { label: '项目阶段', name: '', type: 'picker', required: true, options: '[{\"text\":\"创意阶段\",\"disabled\":false},{\"text\":\"初创阶段\",\"disabled\":false},{\"text\":\"成长阶段\",\"disabled\":false},{\"text\":\"成熟阶段\",\"disabled\":true}]' },\r\n          { label: '报名日期', name: '', type: 'date', required: false, options: '' },\r\n          { label: '项目计划书', name: '', type: 'file', required: true, options: '' },\r\n          { label: '演示视频', name: '', type: 'file', required: false, options: '' }\r\n        ]\r\n      };\r\n\r\n      if (templates[command]) {\r\n        this.currentFormFields = templates[command].map(field => ({\r\n          ...field,\r\n          name: this.generateUniqueFieldName(field.label.toLowerCase())\r\n        }));\r\n      }\r\n    },\r\n    /** 预览表单 */\r\n    handlePreviewForm() {\r\n      this.previewDialogVisible = true;\r\n    },\r\n    /** 保存表单配置 */\r\n    saveFormConfig() {\r\n      if (!this.currentConfig) {\r\n        this.$modal.msgError(\"请先选择要配置的表单\");\r\n        return;\r\n      }\r\n\r\n      // 验证字段配置\r\n      for (let i = 0; i < this.currentFormFields.length; i++) {\r\n        const field = this.currentFormFields[i];\r\n        if (!field.label) {\r\n          this.$modal.msgError(`第${i + 1}个字段的标签不能为空`);\r\n          return;\r\n        }\r\n        if (!field.name) {\r\n          field.name = this.generateUniqueFieldName(field.label.toLowerCase());\r\n        }\r\n        if (this.needOptions(field.type) && !field.options) {\r\n          this.$modal.msgError(`第${i + 1}个字段\"${field.label}\"需要设置选项内容`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      const formData = {\r\n        configId: this.currentConfig.configId,\r\n        formConfig: JSON.stringify(this.currentFormFields)\r\n      };\r\n\r\n      updateFormConfig(formData).then(response => {\r\n        this.$modal.msgSuccess(\"表单配置保存成功\");\r\n        this.formConfigOpen = false;\r\n        this.loadEnabledConfig();\r\n        this.getList();\r\n      });\r\n    },\r\n    /** 获取字段选项 */\r\n    getFieldOptions(options) {\r\n      if (!options) return [];\r\n      return options.split(',').map(opt => opt.trim()).filter(opt => opt);\r\n    },\r\n    /** 获取滚动单选选项 */\r\n    getPickerOptions(options) {\r\n      if (!options) return [];\r\n      try {\r\n        const parsed = JSON.parse(options);\r\n        // 确保返回的是数组格式\r\n        return Array.isArray(parsed) ? parsed : [];\r\n      } catch (e) {\r\n        // 如果解析失败，尝试按逗号分隔的格式处理\r\n        if (typeof options === 'string' && options.trim()) {\r\n          return options.split(',').map(opt => ({\r\n            text: opt.trim(),\r\n            disabled: false\r\n          })).filter(opt => opt.text);\r\n        }\r\n        return [];\r\n      }\r\n    },\r\n    /** 添加滚动单选选项 */\r\n    addPickerOption(field) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n      if (!this.pickerEditData[fieldKey]) {\r\n        this.pickerEditData[fieldKey] = this.getPickerOptions(field.options);\r\n      }\r\n      this.pickerEditData[fieldKey].push({\r\n        text: '',\r\n        disabled: false\r\n      });\r\n      this.updatePickerOptionsFromEdit(field);\r\n    },\r\n    /** 删除滚动单选选项 */\r\n    removePickerOption(field, index) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n      if (this.pickerEditData[fieldKey]) {\r\n        this.pickerEditData[fieldKey].splice(index, 1);\r\n        this.updatePickerOptionsFromEdit(field);\r\n      }\r\n    },\r\n    /** 获取用于编辑的滚动单选选项 */\r\n    getPickerOptionsForEdit(field) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n      if (!this.pickerEditData[fieldKey]) {\r\n        // 初始化编辑数据\r\n        this.pickerEditData[fieldKey] = this.getPickerOptions(field.options);\r\n      }\r\n      return this.pickerEditData[fieldKey];\r\n    },\r\n    /** 从编辑数据更新滚动单选选项 */\r\n    updatePickerOptionsFromEdit(field) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n      if (this.pickerEditData[fieldKey]) {\r\n        field.options = JSON.stringify(this.pickerEditData[fieldKey]);\r\n      }\r\n    },\r\n    /** 更新滚动单选选项 */\r\n    updatePickerOptions(field) {\r\n      // 延迟更新，确保数据已经变更\r\n      this.$nextTick(() => {\r\n        let pickerOptions = this.getPickerOptions(field.options);\r\n        field.options = JSON.stringify(pickerOptions);\r\n      });\r\n    },\r\n    /** 处理字段类型变更 */\r\n    handleFieldTypeChange(field) {\r\n      const fieldKey = field.name || 'temp_' + Date.now();\r\n\r\n      // 当切换到滚动单选时，如果没有选项则初始化\r\n      if (field.type === 'picker') {\r\n        if (!field.options || field.options === '') {\r\n          const defaultOptions = [\r\n            { text: '选项1', disabled: false },\r\n            { text: '选项2', disabled: false }\r\n          ];\r\n          field.options = JSON.stringify(defaultOptions);\r\n          this.pickerEditData[fieldKey] = defaultOptions;\r\n        } else {\r\n          // 重新初始化编辑数据\r\n          this.pickerEditData[fieldKey] = this.getPickerOptions(field.options);\r\n        }\r\n      }\r\n      // 当切换到其他需要选项的字段类型时，如果是JSON格式则转换为逗号分隔\r\n      else if (this.needOptions(field.type) && field.type !== 'picker' && field.options) {\r\n        // 清除编辑数据缓存\r\n        delete this.pickerEditData[fieldKey];\r\n\r\n        try {\r\n          const parsed = JSON.parse(field.options);\r\n          if (Array.isArray(parsed)) {\r\n            field.options = parsed.map(opt => opt.text || opt.label || opt).join(',');\r\n          }\r\n        } catch (e) {\r\n          // 如果不是JSON格式，保持原样\r\n        }\r\n      } else {\r\n        // 清除编辑数据缓存\r\n        delete this.pickerEditData[fieldKey];\r\n      }\r\n    },\r\n    /** 获取选项输入框占位符 */\r\n    getOptionsPlaceholder(type) {\r\n      const placeholders = {\r\n        radio: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\r\n        checkbox: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\r\n        select: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3',\r\n        radio_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\r\n        checkbox_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\r\n        select_other: '选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',\r\n        picker: '滚动单选选项，支持禁用功能，请使用上方的选项配置器进行设置'\r\n      };\r\n      return placeholders[type] || '选项内容，多个选项用逗号分隔';\r\n    },\r\n    /** 获取字段图标 */\r\n    getFieldIcon(type) {\r\n      const icons = {\r\n        input: 'el-icon-edit',\r\n        textarea: 'el-icon-document',\r\n        number: 'el-icon-s-data',\r\n        email: 'el-icon-message',\r\n        tel: 'el-icon-phone',\r\n        radio: 'el-icon-circle-check',\r\n        checkbox: 'el-icon-check',\r\n        select: 'el-icon-arrow-down',\r\n        radio_other: 'el-icon-circle-plus-outline',\r\n        checkbox_other: 'el-icon-circle-plus-outline',\r\n        select_other: 'el-icon-circle-plus-outline',\r\n        picker: 'el-icon-sort',\r\n        date: 'el-icon-date',\r\n        file: 'el-icon-upload'\r\n      };\r\n      return icons[type] || 'el-icon-edit';\r\n    },\r\n    /** 获取字段类型名称 */\r\n    getFieldTypeName(type) {\r\n      const names = {\r\n        input: '文本输入',\r\n        textarea: '多行文本',\r\n        number: '数字输入',\r\n        email: '邮箱',\r\n        tel: '电话',\r\n        radio: '单选',\r\n        checkbox: '多选',\r\n        select: '下拉选择',\r\n        radio_other: '单选+其他',\r\n        checkbox_other: '多选+其他',\r\n        select_other: '下拉+其他',\r\n        picker: '滚动单选',\r\n        date: '日期',\r\n        file: '文件上传'\r\n      };\r\n      return names[type] || '未知类型';\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('miniapp/haitang/formConfig/export', {\r\n        ...this.queryParams\r\n      }, `formConfig_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 赞助商图片上传按钮操作 */\r\n    handleSponsorUpload() {\r\n      this.loadSponsorImage();\r\n      this.sponsorDialogVisible = true;\r\n    },\r\n    /** 加载赞助商图片 */\r\n    loadSponsorImage() {\r\n      getSponsorImage().then(response => {\r\n        if (response.data && response.data.sponsorUnit) {\r\n          this.currentSponsorImage = response.data.sponsorUnit;\r\n        } else {\r\n          this.currentSponsorImage = '';\r\n        }\r\n      }).catch(() => {\r\n        this.currentSponsorImage = '';\r\n      });\r\n    },\r\n    /** 上传前校验 */\r\n    beforeSponsorUpload(file) {\r\n      const isImage = file.type.indexOf('image/') === 0;\r\n      const isLt5M = file.size / 1024 / 1024 < 5;\r\n\r\n      if (!isImage) {\r\n        this.$modal.msgError('上传文件只能是图片格式!');\r\n        return false;\r\n      }\r\n      if (!isLt5M) {\r\n        this.$modal.msgError('上传图片大小不能超过 5MB!');\r\n        return false;\r\n      }\r\n      return true;\r\n    },\r\n    /** 上传成功回调 */\r\n    handleSponsorUploadSuccess(response) {\r\n      if (response.code === 200) {\r\n        const imageUrl = response.url;\r\n        updateSponsorImage(imageUrl).then(() => {\r\n          this.currentSponsorImage = imageUrl;\r\n          this.$modal.msgSuccess('赞助商图片上传成功');\r\n        }).catch(() => {\r\n          this.$modal.msgError('保存赞助商图片失败');\r\n        });\r\n      } else {\r\n        this.$modal.msgError('图片上传失败：' + response.msg);\r\n      }\r\n    },\r\n    /** 上传失败回调 */\r\n    handleSponsorUploadError() {\r\n      this.$modal.msgError('图片上传失败，请重试');\r\n    },\r\n    /** 删除赞助商图片 */\r\n    handleDeleteSponsor() {\r\n      this.$modal.confirm('确认删除当前赞助商图片？').then(() => {\r\n        updateSponsorImage('').then(() => {\r\n          this.currentSponsorImage = '';\r\n          this.$modal.msgSuccess('赞助商图片删除成功');\r\n        }).catch(() => {\r\n          this.$modal.msgError('删除赞助商图片失败');\r\n        });\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.form-config-container {\r\n  margin-bottom: 20px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.config-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.config-header h3 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.config-content {\r\n  padding: 24px;\r\n  min-height: 200px;\r\n}\r\n\r\n.enabled-config {\r\n  border: 1px solid #67c23a;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  background: #f0f9ff;\r\n}\r\n\r\n.enabled-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.enabled-header h4 {\r\n  margin: 0;\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.enabled-description {\r\n  margin-bottom: 16px;\r\n  color: #606266;\r\n}\r\n\r\n.form-preview h5 {\r\n  margin: 0 0 12px 0;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.field-list {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-item {\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  background: #fafbfc;\r\n}\r\n\r\n.field-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.field-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.field-icon {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.field-label {\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.field-type {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.empty-form, .no-enabled-config {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-form i, .no-enabled-config i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.config-list-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.list-header {\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.list-header h4 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.form-fields-config {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-fields-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.form-fields-list {\r\n  padding: 16px;\r\n}\r\n\r\n.empty-fields {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-fields i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.field-config-item {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  margin-bottom: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-config-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 16px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.field-index {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: #409eff;\r\n  color: white;\r\n  border-radius: 50%;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.field-config-body {\r\n  padding: 12px 16px;\r\n}\r\n\r\n.preview-header {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n}\r\n\r\n.preview-header p {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.preview-field {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.required {\r\n  color: #f56c6c;\r\n  margin-left: 4px;\r\n}\r\n\r\n.preview-input {\r\n  width: 100%;\r\n}\r\n\r\n/* 滚动单选配置样式 */\r\n.picker-options-config {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  padding: 12px;\r\n  background: #fafbfc;\r\n}\r\n\r\n.picker-options-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.picker-options-list {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.picker-option-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 8px;\r\n  padding: 8px;\r\n  background: #fff;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n}\r\n\r\n.picker-option-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.empty-picker-options {\r\n  text-align: center;\r\n  padding: 20px;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 滚动单选预览样式 */\r\n.picker-preview {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.picker-display {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  background: #fff;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.picker-placeholder {\r\n  color: #c0c4cc;\r\n  font-size: 14px;\r\n}\r\n\r\n.picker-options-preview {\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.picker-option-preview {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.picker-option-preview:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.picker-option-preview.disabled {\r\n  color: #c0c4cc;\r\n  background: #f5f7fa;\r\n}\r\n\r\n.disabled-tag {\r\n  font-size: 12px;\r\n  color: #f56c6c;\r\n  background: #fef0f0;\r\n  padding: 2px 6px;\r\n  border-radius: 2px;\r\n}\r\n</style>\r\n\r\n<style scoped>\r\n.form-config-container {\r\n  margin-bottom: 20px;\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.config-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.config-header h3 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n  font-weight: 500;\r\n}\r\n\r\n.config-content {\r\n  padding: 24px;\r\n  min-height: 200px;\r\n}\r\n\r\n.enabled-config {\r\n  border: 1px solid #67c23a;\r\n  border-radius: 6px;\r\n  padding: 16px;\r\n  background: #f0f9ff;\r\n}\r\n\r\n.enabled-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.enabled-header h4 {\r\n  margin: 0;\r\n  color: #67c23a;\r\n  font-size: 16px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.enabled-description {\r\n  margin-bottom: 16px;\r\n  color: #606266;\r\n}\r\n\r\n.form-preview h5 {\r\n  margin: 0 0 12px 0;\r\n  color: #303133;\r\n  font-size: 14px;\r\n}\r\n\r\n.field-list {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-item {\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  background: #fafbfc;\r\n}\r\n\r\n.field-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.field-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.field-icon {\r\n  color: #409eff;\r\n  font-size: 16px;\r\n}\r\n\r\n.field-label {\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.field-type {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.empty-form, .no-enabled-config {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-form i, .no-enabled-config i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.config-list-container {\r\n  background: #fff;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.list-header {\r\n  padding: 20px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.list-header h4 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.form-fields-config {\r\n  max-height: 600px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.form-fields-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.form-fields-list {\r\n  padding: 16px;\r\n}\r\n\r\n.empty-fields {\r\n  text-align: center;\r\n  padding: 40px 20px;\r\n  color: #909399;\r\n}\r\n\r\n.empty-fields i {\r\n  font-size: 48px;\r\n  margin-bottom: 16px;\r\n  display: block;\r\n}\r\n\r\n.field-config-item {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 6px;\r\n  margin-bottom: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.field-config-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 16px;\r\n  background: #fafbfc;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.field-index {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 24px;\r\n  height: 24px;\r\n  background: #409eff;\r\n  color: white;\r\n  border-radius: 50%;\r\n  font-size: 12px;\r\n  font-weight: bold;\r\n}\r\n\r\n.field-config-body {\r\n  padding: 12px 16px;\r\n}\r\n\r\n.preview-header {\r\n  text-align: center;\r\n  margin-bottom: 24px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.preview-header h3 {\r\n  margin: 0 0 8px 0;\r\n  color: #303133;\r\n  font-size: 18px;\r\n}\r\n\r\n.preview-header p {\r\n  margin: 0;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.preview-field {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.preview-label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  color: #303133;\r\n  font-weight: 500;\r\n}\r\n\r\n.required {\r\n  color: #f56c6c;\r\n  margin-left: 4px;\r\n}\r\n\r\n.preview-input {\r\n  width: 100%;\r\n}\r\n\r\n/* 赞助商图片上传样式 */\r\n.sponsor-upload-container {\r\n  padding: 20px;\r\n}\r\n\r\n.current-sponsor {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.current-sponsor h4 {\r\n  margin: 0 0 15px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.sponsor-image-preview {\r\n  text-align: center;\r\n  padding: 20px;\r\n  border: 2px dashed #e4e7ed;\r\n  border-radius: 6px;\r\n  background: #fafbfc;\r\n}\r\n\r\n.sponsor-img {\r\n  max-width: 100%;\r\n  max-height: 200px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.upload-section h4 {\r\n  margin: 0 0 15px 0;\r\n  color: #303133;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.sponsor-uploader {\r\n  width: 100%;\r\n}\r\n\r\n.upload-area {\r\n  width: 100%;\r\n  height: 180px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #fafbfc;\r\n  transition: border-color 0.3s;\r\n}\r\n\r\n.upload-area:hover {\r\n  border-color: #409eff;\r\n}\r\n\r\n.sponsor-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.upload-text {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.upload-tip {\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.sponsor-actions {\r\n  margin-top: 20px;\r\n  text-align: center;\r\n  padding-top: 20px;\r\n  border-top: 1px solid #e4e7ed;\r\n}\r\n</style>\r\n"]}]}