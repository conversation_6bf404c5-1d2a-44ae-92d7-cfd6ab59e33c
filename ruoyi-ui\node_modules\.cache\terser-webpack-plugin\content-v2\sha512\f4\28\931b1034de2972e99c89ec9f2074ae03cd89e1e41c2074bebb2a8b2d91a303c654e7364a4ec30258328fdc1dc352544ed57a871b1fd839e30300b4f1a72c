{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-2715be3d\"],{\"437d\":function(e,t,i){\"use strict\";i(\"520b\")},\"520b\":function(e,t,i){},\"6c91\":function(e,t,i){\"use strict\";i(\"75cb\")},\"75cb\":function(e,t,i){},cd21:function(e,t,i){\"use strict\";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i(\"div\",{staticClass:\"app-container\"},[i(\"div\",{staticClass:\"form-config-container\"},[i(\"div\",{staticClass:\"config-header\"},[i(\"h3\",[e._v(\"项目报名表单配置管理\")]),i(\"div\",{staticClass:\"header-actions\"},[i(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:haitang:project:edit\"],expression:\"['miniapp:haitang:project:edit']\"}],attrs:{type:\"warning\",icon:\"el-icon-picture\"},on:{click:e.handleSponsorUpload}},[e._v(\"赞助商图片\")]),i(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:haitang:formConfig:add\"],expression:\"['miniapp:haitang:formConfig:add']\"}],attrs:{type:\"primary\",icon:\"el-icon-plus\"},on:{click:e.handleAdd}},[e._v(\"新增配置\")])],1)]),i(\"div\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],staticClass:\"config-content\"},[e.enabledConfig?i(\"div\",{staticClass:\"enabled-config\"},[i(\"div\",{staticClass:\"enabled-header\"},[i(\"h4\",[i(\"i\",{staticClass:\"el-icon-check\",staticStyle:{color:\"#67c23a\"}}),e._v(\" 当前启用配置：\"+e._s(e.enabledConfig.configName)+\" \")]),i(\"div\",{staticClass:\"enabled-actions\"},[i(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:haitang:formConfig:edit\"],expression:\"['miniapp:haitang:formConfig:edit']\"}],attrs:{size:\"small\",type:\"primary\",icon:\"el-icon-setting\"},on:{click:function(t){return e.handleFormConfig(e.enabledConfig)}}},[e._v(\"配置表单\")]),i(\"el-button\",{attrs:{size:\"small\",type:\"success\",icon:\"el-icon-view\"},on:{click:function(t){return e.handlePreview(e.enabledConfig)}}},[e._v(\"预览表单\")])],1)]),i(\"div\",{staticClass:\"enabled-description\"},[i(\"p\",[e._v(e._s(e.enabledConfig.configDescription||\"暂无描述\"))])]),e.enabledFormFields.length>0?i(\"div\",{staticClass:\"form-preview\"},[i(\"h5\",[e._v(\"表单字段预览：\")]),i(\"div\",{staticClass:\"field-list\"},e._l(e.enabledFormFields,(function(t,a){return i(\"div\",{key:a,staticClass:\"field-item\"},[i(\"div\",{staticClass:\"field-info\"},[i(\"i\",{staticClass:\"field-icon\",class:e.getFieldIcon(t.type)}),i(\"span\",{staticClass:\"field-label\"},[e._v(e._s(t.label))]),i(\"el-tag\",{attrs:{size:\"mini\",type:\"success\"}},[e._v(e._s(t.name))]),t.required?i(\"el-tag\",{attrs:{size:\"mini\",type:\"danger\"}},[e._v(\"必填\")]):i(\"el-tag\",{attrs:{size:\"mini\",type:\"info\"}},[e._v(\"选填\")]),i(\"span\",{staticClass:\"field-type\"},[e._v(e._s(e.getFieldTypeName(t.type)))])],1)])})),0)]):i(\"div\",{staticClass:\"empty-form\"},[i(\"i\",{staticClass:\"el-icon-document-add\"}),i(\"p\",[e._v('该配置暂未设置表单字段，点击\"配置表单\"开始设置')])])]):i(\"div\",{staticClass:\"no-enabled-config\"},[i(\"i\",{staticClass:\"el-icon-warning-outline\"}),i(\"p\",[e._v(\"暂无启用的表单配置，请先创建并启用一个配置\")])])])]),i(\"div\",{staticClass:\"config-list-container\"},[i(\"div\",{staticClass:\"list-header\"},[i(\"h4\",[e._v(\"所有表单配置\")]),i(\"el-form\",{ref:\"queryForm\",attrs:{model:e.queryParams,size:\"small\",inline:!0,\"label-width\":\"80px\"}},[i(\"el-form-item\",{attrs:{label:\"配置名称\",prop:\"configName\"}},[i(\"el-input\",{staticStyle:{width:\"200px\"},attrs:{placeholder:\"请输入配置名称\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.configName,callback:function(t){e.$set(e.queryParams,\"configName\",t)},expression:\"queryParams.configName\"}})],1),i(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[i(\"el-select\",{staticStyle:{width:\"120px\"},attrs:{placeholder:\"请选择状态\",clearable:\"\"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,\"status\",t)},expression:\"queryParams.status\"}},e._l(e.dict.type.sys_normal_disable,(function(e){return i(\"el-option\",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),i(\"el-form-item\",[i(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:e.handleQuery}},[e._v(\"搜索\")]),i(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:e.resetQuery}},[e._v(\"重置\")])],1)],1)],1),i(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.listLoading,expression:\"listLoading\"}],attrs:{data:e.formConfigList},on:{\"selection-change\":e.handleSelectionChange}},[i(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),i(\"el-table-column\",{attrs:{label:\"配置名称\",align:\"center\",prop:\"configName\",\"min-width\":\"150\"}}),i(\"el-table-column\",{attrs:{label:\"配置描述\",align:\"center\",prop:\"configDescription\",\"show-overflow-tooltip\":\"\",\"min-width\":\"200\"}}),i(\"el-table-column\",{attrs:{label:\"是否启用\",align:\"center\",prop:\"isEnabled\",width:\"100\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[\"1\"===t.row.isEnabled?i(\"el-tag\",{attrs:{type:\"success\"}},[e._v(\"已启用\")]):i(\"el-tag\",{attrs:{type:\"info\"}},[e._v(\"未启用\")])]}}])}),i(\"el-table-column\",{attrs:{label:\"状态\",align:\"center\",prop:\"status\",width:\"80\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[i(\"dict-tag\",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}])}),i(\"el-table-column\",{attrs:{label:\"创建时间\",align:\"center\",prop:\"createTime\",width:\"160\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[i(\"span\",[e._v(e._s(e.parseTime(t.row.createTime,\"{y}-{m}-{d} {h}:{i}\")))])]}}])}),i(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",\"class-name\":\"small-padding fixed-width\",width:\"200\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[i(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:haitang:formConfig:edit\"],expression:\"['miniapp:haitang:formConfig:edit']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-setting\"},on:{click:function(i){return e.handleFormConfig(t.row)}}},[e._v(\"配置\")]),i(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:haitang:formConfig:edit\"],expression:\"['miniapp:haitang:formConfig:edit']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-edit\"},on:{click:function(i){return e.handleUpdate(t.row)}}},[e._v(\"编辑\")]),\"1\"!==t.row.isEnabled?i(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:haitang:formConfig:edit\"],expression:\"['miniapp:haitang:formConfig:edit']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-check\"},on:{click:function(i){return e.handleEnable(t.row)}}},[e._v(\"启用\")]):e._e(),i(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:haitang:formConfig:remove\"],expression:\"['miniapp:haitang:formConfig:remove']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-delete\"},on:{click:function(i){return e.handleDelete(t.row)}}},[e._v(\"删除\")])]}}])})],1),i(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.total>0,expression:\"total>0\"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{\"update:page\":function(t){return e.$set(e.queryParams,\"pageNum\",t)},\"update:limit\":function(t){return e.$set(e.queryParams,\"pageSize\",t)},pagination:e.getList}})],1),i(\"el-dialog\",{attrs:{title:e.title,visible:e.open,width:\"600px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.open=t}}},[i(\"el-form\",{ref:\"form\",attrs:{model:e.form,rules:e.rules,\"label-width\":\"100px\"}},[i(\"el-form-item\",{attrs:{label:\"配置名称\",prop:\"configName\"}},[i(\"el-input\",{attrs:{placeholder:\"请输入配置名称\"},model:{value:e.form.configName,callback:function(t){e.$set(e.form,\"configName\",t)},expression:\"form.configName\"}})],1),i(\"el-form-item\",{attrs:{label:\"配置描述\",prop:\"configDescription\"}},[i(\"el-input\",{attrs:{type:\"textarea\",rows:3,placeholder:\"请输入配置描述\"},model:{value:e.form.configDescription,callback:function(t){e.$set(e.form,\"configDescription\",t)},expression:\"form.configDescription\"}})],1),i(\"el-form-item\",{attrs:{label:\"排序\",prop:\"sortOrder\"}},[i(\"el-input-number\",{attrs:{\"controls-position\":\"right\",min:0},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,\"sortOrder\",t)},expression:\"form.sortOrder\"}})],1),i(\"el-form-item\",{attrs:{label:\"状态\",prop:\"status\"}},[i(\"el-radio-group\",{model:{value:e.form.status,callback:function(t){e.$set(e.form,\"status\",t)},expression:\"form.status\"}},e._l(e.dict.type.sys_normal_disable,(function(t){return i(\"el-radio\",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),i(\"el-form-item\",{attrs:{label:\"备注\",prop:\"remark\"}},[i(\"el-input\",{attrs:{type:\"textarea\",rows:2,placeholder:\"请输入备注\"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,\"remark\",t)},expression:\"form.remark\"}})],1)],1),i(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[i(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.submitForm}},[e._v(\"确 定\")]),i(\"el-button\",{on:{click:e.cancel}},[e._v(\"取 消\")])],1)],1),i(\"el-dialog\",{attrs:{title:\"表单字段配置\",visible:e.formConfigOpen,width:\"1000px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.formConfigOpen=t}}},[i(\"div\",{staticClass:\"form-fields-config\"},[i(\"div\",{staticClass:\"form-fields-toolbar\"},[i(\"div\",{staticClass:\"toolbar-left\"},[i(\"el-button\",{attrs:{type:\"primary\",size:\"small\",icon:\"el-icon-plus\"},on:{click:e.addFormField}},[e._v(\" 添加字段 \")]),i(\"el-dropdown\",{attrs:{size:\"small\"},on:{command:e.handleTemplateCommand}},[i(\"el-button\",{attrs:{size:\"small\"}},[e._v(\" 预设模板\"),i(\"i\",{staticClass:\"el-icon-arrow-down el-icon--right\"})]),i(\"el-dropdown-menu\",{attrs:{slot:\"dropdown\"},slot:\"dropdown\"},[i(\"el-dropdown-item\",{attrs:{command:\"basic\"}},[e._v(\"基础信息模板\")]),i(\"el-dropdown-item\",{attrs:{command:\"project\"}},[e._v(\"项目报名模板\")]),i(\"el-dropdown-item\",{attrs:{command:\"clear\"}},[e._v(\"清空所有字段\")])],1)],1)],1),i(\"div\",{staticClass:\"toolbar-right\"},[i(\"el-button\",{attrs:{size:\"small\",icon:\"el-icon-view\"},on:{click:e.handlePreviewForm}},[e._v(\"预览表单\")])],1)]),i(\"div\",{staticClass:\"form-fields-list\"},[0===e.currentFormFields.length?i(\"div\",{staticClass:\"empty-fields\"},[i(\"i\",{staticClass:\"el-icon-document-add\"}),i(\"p\",[e._v('暂无字段，点击\"添加字段\"开始配置')])]):i(\"div\",e._l(e.currentFormFields,(function(t,a){return i(\"div\",{key:a,staticClass:\"field-config-item\"},[i(\"div\",{staticClass:\"field-config-header\"},[i(\"span\",{staticClass:\"field-index\"},[e._v(e._s(a+1))]),i(\"el-input\",{staticStyle:{width:\"150px\"},attrs:{placeholder:\"字段标签\",size:\"small\"},model:{value:t.label,callback:function(i){e.$set(t,\"label\",i)},expression:\"field.label\"}}),i(\"el-input\",{staticStyle:{width:\"120px\"},attrs:{placeholder:\"字段名称\",size:\"small\"},model:{value:t.name,callback:function(i){e.$set(t,\"name\",i)},expression:\"field.name\"}}),i(\"el-select\",{staticStyle:{width:\"140px\"},attrs:{placeholder:\"字段类型\",size:\"small\"},on:{change:function(i){return e.handleFieldTypeChange(t)}},model:{value:t.type,callback:function(i){e.$set(t,\"type\",i)},expression:\"field.type\"}},[i(\"el-option\",{attrs:{label:\"📝 文本输入\",value:\"input\"}}),i(\"el-option\",{attrs:{label:\"📄 多行文本\",value:\"textarea\"}}),i(\"el-option\",{attrs:{label:\"🔢 数字输入\",value:\"number\"}}),i(\"el-option\",{attrs:{label:\"📧 邮箱\",value:\"email\"}}),i(\"el-option\",{attrs:{label:\"📞 电话\",value:\"tel\"}}),i(\"el-option\",{attrs:{label:\"🔘 单选\",value:\"radio\"}}),i(\"el-option\",{attrs:{label:\"☑️ 多选\",value:\"checkbox\"}}),i(\"el-option\",{attrs:{label:\"📋 下拉选择\",value:\"select\"}}),i(\"el-option\",{attrs:{label:\"🔘➕ 单选+其他\",value:\"radio_other\"}}),i(\"el-option\",{attrs:{label:\"☑️➕ 多选+其他\",value:\"checkbox_other\"}}),i(\"el-option\",{attrs:{label:\"📋➕ 下拉+其他\",value:\"select_other\"}}),i(\"el-option\",{attrs:{label:\"🎡 滚动单选\",value:\"picker\"}}),i(\"el-option\",{attrs:{label:\"📅 日期\",value:\"date\"}}),i(\"el-option\",{attrs:{label:\"📎 文件上传\",value:\"file\"}})],1),i(\"el-checkbox\",{attrs:{size:\"small\"},model:{value:t.required,callback:function(i){e.$set(t,\"required\",i)},expression:\"field.required\"}},[e._v(\"必填\")]),i(\"el-button\",{attrs:{size:\"mini\",type:\"danger\",icon:\"el-icon-delete\"},on:{click:function(t){return e.removeFormField(a)}}},[e._v(\"删除\")])],1),e.needOptions(t.type)?i(\"div\",{staticClass:\"field-config-body\"},[\"picker\"===t.type?i(\"div\",{staticClass:\"picker-options-config\"},[i(\"div\",{staticClass:\"picker-options-header\"},[i(\"span\",[e._v(\"选项配置（支持禁用选项）\")]),i(\"el-button\",{attrs:{size:\"mini\",type:\"primary\"},on:{click:function(i){return e.addPickerOption(t)}}},[e._v(\"添加选项\")])],1),i(\"div\",{staticClass:\"picker-options-list\"},e._l(e.getPickerOptionsForEdit(t),(function(a,n){return i(\"div\",{key:n,staticClass:\"picker-option-item\"},[i(\"el-input\",{staticStyle:{width:\"200px\"},attrs:{placeholder:\"选项内容\",size:\"small\"},on:{input:function(i){return e.updatePickerOptionsFromEdit(t)}},model:{value:a.text,callback:function(t){e.$set(a,\"text\",t)},expression:\"option.text\"}}),i(\"el-checkbox\",{attrs:{size:\"small\"},on:{change:function(i){return e.updatePickerOptionsFromEdit(t)}},model:{value:a.disabled,callback:function(t){e.$set(a,\"disabled\",t)},expression:\"option.disabled\"}},[e._v(\"禁用\")]),i(\"el-button\",{attrs:{size:\"mini\",type:\"danger\",icon:\"el-icon-delete\"},on:{click:function(i){return e.removePickerOption(t,n)}}},[e._v(\"删除\")])],1)})),0),0===e.getPickerOptionsForEdit(t).length?i(\"div\",{staticClass:\"empty-picker-options\"},[i(\"span\",[e._v('暂无选项，点击\"添加选项\"开始配置')])]):e._e()]):i(\"el-input\",{attrs:{placeholder:e.getOptionsPlaceholder(t.type),size:\"small\"},model:{value:t.options,callback:function(i){e.$set(t,\"options\",i)},expression:\"field.options\"}})],1):e._e()])})),0)])]),i(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[i(\"el-button\",{attrs:{type:\"primary\"},on:{click:e.saveFormConfig}},[e._v(\"保存配置\")]),i(\"el-button\",{on:{click:function(t){e.formConfigOpen=!1}}},[e._v(\"取 消\")])],1)]),i(\"el-dialog\",{attrs:{title:\"表单预览\",visible:e.previewDialogVisible,width:\"600px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.previewDialogVisible=t}}},[i(\"div\",{staticClass:\"form-preview\"},[i(\"div\",{staticClass:\"preview-header\"},[i(\"h3\",[e._v(e._s(e.currentConfig?e.currentConfig.configName:\"项目报名表\"))]),i(\"p\",[e._v(\"请填写以下信息完成报名\")])]),i(\"div\",{staticClass:\"preview-form\"},e._l(e.currentFormFields,(function(t,a){return i(\"div\",{key:a,staticClass:\"preview-field\"},[i(\"label\",{staticClass:\"preview-label\"},[e._v(\" \"+e._s(t.label)+\" \"),t.required?i(\"span\",{staticClass:\"required\"},[e._v(\"*\")]):e._e()]),i(\"div\",{staticClass:\"preview-input\"},[[\"input\",\"email\",\"tel\"].includes(t.type)?i(\"el-input\",{attrs:{placeholder:\"请输入\"+t.label,size:\"small\",disabled:\"\"}}):\"textarea\"===t.type?i(\"el-input\",{attrs:{type:\"textarea\",placeholder:\"请输入\"+t.label,size:\"small\",disabled:\"\"}}):\"number\"===t.type?i(\"el-input-number\",{attrs:{placeholder:\"请输入\"+t.label,size:\"small\",disabled:\"\"}}):\"radio\"===t.type?i(\"el-radio-group\",{attrs:{disabled:\"\"}},e._l(e.getFieldOptions(t.options),(function(t){return i(\"el-radio\",{key:t,attrs:{label:t}},[e._v(e._s(t))])})),1):\"radio_other\"===t.type?i(\"div\",[i(\"el-radio-group\",{staticStyle:{\"margin-bottom\":\"8px\"},attrs:{disabled:\"\"}},[e._l(e.getFieldOptions(t.options),(function(t){return i(\"el-radio\",{key:t,attrs:{label:t}},[e._v(e._s(t))])})),i(\"el-radio\",{attrs:{label:\"其他\"}},[e._v(\"其他\")])],2),i(\"el-input\",{attrs:{placeholder:\"选择'其他'时请在此输入具体内容\",size:\"small\",disabled:\"\"}})],1):\"checkbox\"===t.type?i(\"el-checkbox-group\",{attrs:{disabled:\"\"}},e._l(e.getFieldOptions(t.options),(function(t){return i(\"el-checkbox\",{key:t,attrs:{label:t}},[e._v(e._s(t))])})),1):\"checkbox_other\"===t.type?i(\"div\",[i(\"el-checkbox-group\",{staticStyle:{\"margin-bottom\":\"8px\"},attrs:{disabled:\"\"}},[e._l(e.getFieldOptions(t.options),(function(t){return i(\"el-checkbox\",{key:t,attrs:{label:t}},[e._v(e._s(t))])})),i(\"el-checkbox\",{attrs:{label:\"其他\"}},[e._v(\"其他\")])],2),i(\"el-input\",{attrs:{placeholder:\"选择'其他'时请在此输入具体内容\",size:\"small\",disabled:\"\"}})],1):\"select\"===t.type?i(\"el-select\",{attrs:{placeholder:\"请选择\"+t.label,size:\"small\",disabled:\"\"}},e._l(e.getFieldOptions(t.options),(function(e){return i(\"el-option\",{key:e,attrs:{label:e,value:e}})})),1):\"select_other\"===t.type?i(\"div\",[i(\"el-select\",{staticStyle:{\"margin-bottom\":\"8px\",width:\"100%\"},attrs:{placeholder:\"请选择\"+t.label,size:\"small\",disabled:\"\"}},[e._l(e.getFieldOptions(t.options),(function(e){return i(\"el-option\",{key:e,attrs:{label:e,value:e}})})),i(\"el-option\",{attrs:{label:\"其他\",value:\"其他\"}})],2),i(\"el-input\",{attrs:{placeholder:\"选择'其他'时请在此输入具体内容\",size:\"small\",disabled:\"\"}})],1):\"picker\"===t.type?i(\"div\",{staticClass:\"picker-preview\"},[i(\"div\",{staticClass:\"picker-display\"},[i(\"span\",{staticClass:\"picker-placeholder\"},[e._v(e._s(\"请选择\"+t.label))]),i(\"i\",{staticClass:\"el-icon-arrow-down\"})]),i(\"div\",{staticClass:\"picker-options-preview\"},e._l(e.getPickerOptions(t.options),(function(t,a){return i(\"div\",{key:a,class:[\"picker-option-preview\",{disabled:t.disabled}]},[e._v(\" \"+e._s(t.text)+\" \"),t.disabled?i(\"span\",{staticClass:\"disabled-tag\"},[e._v(\"禁用\")]):e._e()])})),0)]):\"date\"===t.type?i(\"el-date-picker\",{attrs:{type:\"date\",placeholder:\"请选择\"+t.label,size:\"small\",disabled:\"\"}}):\"file\"===t.type?i(\"el-upload\",{staticClass:\"upload-demo\",attrs:{action:\"#\",\"auto-upload\":!1,disabled:\"\"}},[i(\"el-button\",{attrs:{size:\"small\",type:\"primary\",disabled:\"\"}},[e._v(\"点击上传\")])],1):e._e()],1)])})),0)]),i(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[i(\"el-button\",{on:{click:function(t){e.previewDialogVisible=!1}}},[e._v(\"关 闭\")])],1)]),i(\"el-dialog\",{attrs:{title:\"赞助商图片管理\",visible:e.sponsorDialogVisible,width:\"600px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.sponsorDialogVisible=t}}},[i(\"div\",{staticClass:\"sponsor-upload-container\"},[e.currentSponsorImage?i(\"div\",{staticClass:\"current-sponsor\"},[i(\"h4\",[e._v(\"当前赞助商图片\")]),i(\"div\",{staticClass:\"sponsor-image-preview\"},[i(\"img\",{staticClass:\"sponsor-img\",attrs:{src:e.currentSponsorImage,alt:\"赞助商图片\"}})])]):e._e(),i(\"div\",{staticClass:\"upload-section\"},[i(\"h4\",[e._v(e._s(e.currentSponsorImage?\"更换赞助商图片\":\"上传赞助商图片\"))]),i(\"el-upload\",{staticClass:\"sponsor-uploader\",attrs:{action:e.uploadAction,headers:e.uploadHeaders,\"show-file-list\":!1,\"on-success\":e.handleSponsorUploadSuccess,\"on-error\":e.handleSponsorUploadError,\"before-upload\":e.beforeSponsorUpload,accept:\"image/*\"}},[i(\"div\",{staticClass:\"upload-area\"},[i(\"i\",{staticClass:\"el-icon-plus sponsor-uploader-icon\"}),i(\"div\",{staticClass:\"upload-text\"},[e._v(\"点击上传图片\")]),i(\"div\",{staticClass:\"upload-tip\"},[e._v(\"支持 JPG、PNG 格式，建议尺寸 400x200px\")])])])],1),e.currentSponsorImage?i(\"div\",{staticClass:\"sponsor-actions\"},[i(\"el-button\",{attrs:{type:\"danger\"},on:{click:e.handleDeleteSponsor}},[e._v(\"删除当前图片\")])],1):e._e()]),i(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[i(\"el-button\",{on:{click:function(t){e.sponsorDialogVisible=!1}}},[e._v(\"关 闭\")])],1)])],1)},n=[],o=i(\"5530\"),l=(i(\"99af\"),i(\"4de4\"),i(\"caad\"),i(\"a15b\"),i(\"d81d\"),i(\"14d9\"),i(\"a434\"),i(\"b0c0\"),i(\"e9c4\"),i(\"b64b\"),i(\"d3b7\"),i(\"498a\"),i(\"0643\"),i(\"2382\"),i(\"4e3e\"),i(\"a573\"),i(\"9a9a\"),i(\"159b\"),i(\"b775\"));function r(e){return Object(l[\"a\"])({url:\"/miniapp/haitang/formConfig/list\",method:\"get\",params:e})}function s(e){return Object(l[\"a\"])({url:\"/miniapp/haitang/formConfig/\"+e,method:\"get\"})}function c(e){return Object(l[\"a\"])({url:\"/miniapp/haitang/formConfig\",method:\"post\",data:e})}function d(e){return Object(l[\"a\"])({url:\"/miniapp/haitang/formConfig\",method:\"put\",data:e})}function p(e){return Object(l[\"a\"])({url:\"/miniapp/haitang/formConfig/\"+e,method:\"delete\"})}function u(e){return Object(l[\"a\"])({url:\"/miniapp/haitang/formConfig/enable/\"+e,method:\"put\"})}function m(){return Object(l[\"a\"])({url:\"/miniapp/haitang/project/config/sponsor\",method:\"get\"})}function f(e){return Object(l[\"a\"])({url:\"/miniapp/haitang/project/config/sponsor\",method:\"put\",data:{sponsorUnit:e}})}var h=i(\"5f87\"),g={name:\"FormConfig\",dicts:[\"sys_normal_disable\"],data:function(){return{loading:!0,listLoading:!1,ids:[],single:!0,multiple:!0,total:0,formConfigList:[],enabledConfig:null,enabledFormFields:[],title:\"\",open:!1,formConfigOpen:!1,previewDialogVisible:!1,currentFormFields:[],currentConfig:null,pickerEditData:{},sponsorDialogVisible:!1,currentSponsorImage:\"\",uploadAction:\"/prod-api/common/upload\",uploadHeaders:{Authorization:\"Bearer \"+Object(h[\"a\"])()},queryParams:{pageNum:1,pageSize:10,configName:null,status:null},form:{},rules:{configName:[{required:!0,message:\"配置名称不能为空\",trigger:\"blur\"}]}}},created:function(){this.loadEnabledConfig(),this.getList()},methods:{loadEnabledConfig:function(){var e=this;this.loading=!0,Object(l[\"a\"])({url:\"/miniapp/haitang/formConfig/enabled\",method:\"get\"}).then((function(t){t.data?(e.enabledConfig=t.data,e.loadEnabledFormFields()):(e.enabledConfig=null,e.enabledFormFields=[]),e.loading=!1})).catch((function(){e.enabledConfig=null,e.enabledFormFields=[],e.loading=!1}))},loadEnabledFormFields:function(){if(this.enabledConfig&&this.enabledConfig.formConfig)try{this.enabledFormFields=JSON.parse(this.enabledConfig.formConfig)}catch(e){this.enabledFormFields=[]}else this.enabledFormFields=[]},getList:function(){var e=this;this.listLoading=!0,r(this.queryParams).then((function(t){e.formConfigList=t.rows,e.total=t.total,e.listLoading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={configId:null,configName:null,configDescription:null,formConfig:null,isEnabled:\"0\",sortOrder:0,status:\"0\",createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm(\"form\")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm(\"queryForm\"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.configId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title=\"添加天大海棠杯项目报名表单配置\"},handleUpdate:function(e){var t=this;this.reset();var i=e.configId||this.ids;s(i).then((function(e){t.form=e.data,t.open=!0,t.title=\"修改天大海棠杯项目报名表单配置\"}))},submitForm:function(){var e=this;this.$refs[\"form\"].validate((function(t){t&&(null!=e.form.configId?d(e.form).then((function(t){e.$modal.msgSuccess(\"修改成功\"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess(\"新增成功\"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,i=e.configId||this.ids;this.$modal.confirm('是否确认删除天大海棠杯项目报名表单配置编号为\"'+i+'\"的数据项？').then((function(){return p(i)})).then((function(){t.getList(),t.$modal.msgSuccess(\"删除成功\")})).catch((function(){}))},handleFormConfig:function(e){var t=this;if(this.currentConfig=e,this.currentFormFields=[],this.pickerEditData={},e.formConfig)try{this.currentFormFields=JSON.parse(e.formConfig),this.currentFormFields.forEach((function(e){if(\"picker\"===e.type&&e.options){var i=e.name||\"temp_\"+Date.now();t.pickerEditData[i]=t.getPickerOptions(e.options)}}))}catch(i){this.currentFormFields=[]}this.formConfigOpen=!0},handlePreview:function(e){if(this.currentConfig=e,this.currentFormFields=[],e.formConfig)try{this.currentFormFields=JSON.parse(e.formConfig)}catch(t){this.currentFormFields=[]}this.previewDialogVisible=!0},handleEnable:function(e){var t=this;this.$modal.confirm(\"启用此配置将禁用其他所有配置，是否确认启用？\").then((function(){return u(e.configId)})).then((function(){t.loadEnabledConfig(),t.getList(),t.$modal.msgSuccess(\"启用成功\")})).catch((function(){}))},addFormField:function(){var e=this.generateUniqueFieldName(\"field\"),t={name:e,label:\"\",type:\"input\",required:!1,options:\"\"};this.currentFormFields.push(t)},removeFormField:function(e){this.currentFormFields.splice(e,1)},generateUniqueFieldName:function(e){var t=1,i=e+t;while(this.currentFormFields.some((function(e){return e.name===i})))t++,i=e+t;return i},needOptions:function(e){return[\"radio\",\"checkbox\",\"select\",\"radio_other\",\"checkbox_other\",\"select_other\",\"picker\"].includes(e)},handleTemplateCommand:function(e){var t=this;if(\"clear\"!==e){var i={basic:[{label:\"姓名\",name:\"\",type:\"input\",required:!0,options:\"\"},{label:\"联系电话\",name:\"\",type:\"tel\",required:!0,options:\"\"},{label:\"邮箱地址\",name:\"\",type:\"email\",required:!1,options:\"\"}],project:[{label:\"项目名称\",name:\"\",type:\"input\",required:!0,options:\"\"},{label:\"团队负责人\",name:\"\",type:\"input\",required:!0,options:\"\"},{label:\"联系电话\",name:\"\",type:\"tel\",required:!0,options:\"\"},{label:\"邮箱地址\",name:\"\",type:\"email\",required:!0,options:\"\"},{label:\"项目简介\",name:\"\",type:\"textarea\",required:!0,options:\"\"},{label:\"项目类型\",name:\"\",type:\"select_other\",required:!0,options:\"科技创新,商业模式,社会公益,文化创意\"},{label:\"团队规模\",name:\"\",type:\"radio\",required:!0,options:\"1人,2-3人,4-5人,6-10人,10人以上\"},{label:\"项目阶段\",name:\"\",type:\"picker\",required:!0,options:'[{\"text\":\"创意阶段\",\"disabled\":false},{\"text\":\"初创阶段\",\"disabled\":false},{\"text\":\"成长阶段\",\"disabled\":false},{\"text\":\"成熟阶段\",\"disabled\":true}]'},{label:\"报名日期\",name:\"\",type:\"date\",required:!1,options:\"\"},{label:\"项目计划书\",name:\"\",type:\"file\",required:!0,options:\"\"},{label:\"演示视频\",name:\"\",type:\"file\",required:!1,options:\"\"}]};i[e]&&(this.currentFormFields=i[e].map((function(e){return Object(o[\"a\"])(Object(o[\"a\"])({},e),{},{name:t.generateUniqueFieldName(e.label.toLowerCase())})})))}else this.$modal.confirm(\"确认清空所有字段？\").then((function(){t.currentFormFields=[]}))},handlePreviewForm:function(){this.previewDialogVisible=!0},saveFormConfig:function(){var e=this;if(this.currentConfig){for(var t=0;t<this.currentFormFields.length;t++){var i=this.currentFormFields[t];if(!i.label)return void this.$modal.msgError(\"第\".concat(t+1,\"个字段的标签不能为空\"));if(i.name||(i.name=this.generateUniqueFieldName(i.label.toLowerCase())),this.needOptions(i.type)&&!i.options)return void this.$modal.msgError(\"第\".concat(t+1,'个字段\"').concat(i.label,'\"需要设置选项内容'))}var a={configId:this.currentConfig.configId,formConfig:JSON.stringify(this.currentFormFields)};d(a).then((function(t){e.$modal.msgSuccess(\"表单配置保存成功\"),e.formConfigOpen=!1,e.loadEnabledConfig(),e.getList()}))}else this.$modal.msgError(\"请先选择要配置的表单\")},getFieldOptions:function(e){return e?e.split(\",\").map((function(e){return e.trim()})).filter((function(e){return e})):[]},getPickerOptions:function(e){if(!e)return[];try{var t=JSON.parse(e);return Array.isArray(t)?t:[]}catch(i){return\"string\"===typeof e&&e.trim()?e.split(\",\").map((function(e){return{text:e.trim(),disabled:!1}})).filter((function(e){return e.text})):[]}},addPickerOption:function(e){var t=e.name||\"temp_\"+Date.now();this.pickerEditData[t]||(this.pickerEditData[t]=this.getPickerOptions(e.options)),this.pickerEditData[t].push({text:\"\",disabled:!1}),this.updatePickerOptionsFromEdit(e)},removePickerOption:function(e,t){var i=e.name||\"temp_\"+Date.now();this.pickerEditData[i]&&(this.pickerEditData[i].splice(t,1),this.updatePickerOptionsFromEdit(e))},getPickerOptionsForEdit:function(e){var t=e.name||\"temp_\"+Date.now();return this.pickerEditData[t]||(this.pickerEditData[t]=this.getPickerOptions(e.options)),this.pickerEditData[t]},updatePickerOptionsFromEdit:function(e){var t=e.name||\"temp_\"+Date.now();this.pickerEditData[t]&&(e.options=JSON.stringify(this.pickerEditData[t]))},updatePickerOptions:function(e){var t=this;this.$nextTick((function(){var i=t.getPickerOptions(e.options);e.options=JSON.stringify(i)}))},handleFieldTypeChange:function(e){var t=e.name||\"temp_\"+Date.now();if(\"picker\"===e.type)if(e.options&&\"\"!==e.options)this.pickerEditData[t]=this.getPickerOptions(e.options);else{var i=[{text:\"选项1\",disabled:!1},{text:\"选项2\",disabled:!1}];e.options=JSON.stringify(i),this.pickerEditData[t]=i}else if(this.needOptions(e.type)&&\"picker\"!==e.type&&e.options){delete this.pickerEditData[t];try{var a=JSON.parse(e.options);Array.isArray(a)&&(e.options=a.map((function(e){return e.text||e.label||e})).join(\",\"))}catch(n){}}else delete this.pickerEditData[t]},getOptionsPlaceholder:function(e){var t={radio:\"选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3\",checkbox:\"选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3\",select:\"选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3\",radio_other:'选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',checkbox_other:'选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',select_other:'选项内容，多个选项用逗号分隔，如：选项1,选项2,选项3（会自动添加\"其他\"选项）',picker:\"滚动单选选项，支持禁用功能，请使用上方的选项配置器进行设置\"};return t[e]||\"选项内容，多个选项用逗号分隔\"},getFieldIcon:function(e){var t={input:\"el-icon-edit\",textarea:\"el-icon-document\",number:\"el-icon-s-data\",email:\"el-icon-message\",tel:\"el-icon-phone\",radio:\"el-icon-circle-check\",checkbox:\"el-icon-check\",select:\"el-icon-arrow-down\",radio_other:\"el-icon-circle-plus-outline\",checkbox_other:\"el-icon-circle-plus-outline\",select_other:\"el-icon-circle-plus-outline\",picker:\"el-icon-sort\",date:\"el-icon-date\",file:\"el-icon-upload\"};return t[e]||\"el-icon-edit\"},getFieldTypeName:function(e){var t={input:\"文本输入\",textarea:\"多行文本\",number:\"数字输入\",email:\"邮箱\",tel:\"电话\",radio:\"单选\",checkbox:\"多选\",select:\"下拉选择\",radio_other:\"单选+其他\",checkbox_other:\"多选+其他\",select_other:\"下拉+其他\",picker:\"滚动单选\",date:\"日期\",file:\"文件上传\"};return t[e]||\"未知类型\"},handleExport:function(){this.download(\"miniapp/haitang/formConfig/export\",Object(o[\"a\"])({},this.queryParams),\"formConfig_\".concat((new Date).getTime(),\".xlsx\"))},handleSponsorUpload:function(){this.loadSponsorImage(),this.sponsorDialogVisible=!0},loadSponsorImage:function(){var e=this;m().then((function(t){t.data&&t.data.sponsorUnit?e.currentSponsorImage=t.data.sponsorUnit:e.currentSponsorImage=\"\"})).catch((function(){e.currentSponsorImage=\"\"}))},beforeSponsorUpload:function(e){var t=0===e.type.indexOf(\"image/\"),i=e.size/1024/1024<5;return t?!!i||(this.$modal.msgError(\"上传图片大小不能超过 5MB!\"),!1):(this.$modal.msgError(\"上传文件只能是图片格式!\"),!1)},handleSponsorUploadSuccess:function(e){var t=this;if(200===e.code){var i=e.url;f(i).then((function(){t.currentSponsorImage=i,t.$modal.msgSuccess(\"赞助商图片上传成功\")})).catch((function(){t.$modal.msgError(\"保存赞助商图片失败\")}))}else this.$modal.msgError(\"图片上传失败：\"+e.msg)},handleSponsorUploadError:function(){this.$modal.msgError(\"图片上传失败，请重试\")},handleDeleteSponsor:function(){var e=this;this.$modal.confirm(\"确认删除当前赞助商图片？\").then((function(){f(\"\").then((function(){e.currentSponsorImage=\"\",e.$modal.msgSuccess(\"赞助商图片删除成功\")})).catch((function(){e.$modal.msgError(\"删除赞助商图片失败\")}))})).catch((function(){}))}}},b=g,v=(i(\"437d\"),i(\"6c91\"),i(\"2877\")),y=Object(v[\"a\"])(b,a,n,!1,null,\"265cb072\",null);t[\"default\"]=y.exports}}]);", "extractedComments": []}