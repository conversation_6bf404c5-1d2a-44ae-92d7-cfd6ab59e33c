<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.miniapp.mapper.MiniHaitangProjectConfigMapper">

    <select id="selectSponsorUnit" resultType="String">
        select config_value from sys_config where config_key = 'haitang.project.sponsor.unit'
    </select>

    <update id="updateSponsorUnit">
        update sys_config set config_value = #{sponsorUnit} where config_key = 'haitang.project.sponsor.unit'
    </update>

</mapper>
