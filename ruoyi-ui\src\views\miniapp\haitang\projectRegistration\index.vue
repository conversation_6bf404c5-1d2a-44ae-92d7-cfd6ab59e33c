<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户手机号" prop="userPhone">
        <el-input
          v-model="queryParams.userPhone"
          placeholder="请输入用户手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['miniapp:haitang:registration:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['miniapp:haitang:registration:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['miniapp:haitang:registration:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="registrationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="报名ID" align="center" prop="registrationId" />
      <el-table-column label="表单配置" align="center" prop="configName" />
      <el-table-column label="用户姓名" align="center" prop="userName" />
      <el-table-column label="用户手机号" align="center" prop="userPhone" />
      <el-table-column label="报名时间" align="center" prop="registrationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['miniapp:haitang:registration:query']"
          >查看</el-button>

          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['miniapp:haitang:registration:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看报名详情对话框 -->
    <el-dialog title="报名详情" :visible.sync="viewOpen" width="900px" append-to-body>
      <div class="registration-detail">
        <!-- 基本信息 -->
        <div class="basic-info">
          <h3>基本信息</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>表单配置：</label>
                <span>{{ viewForm.configName }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>报名时间：</label>
                <span>{{ parseTime(viewForm.registrationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>用户姓名：</label>
                <span>{{ viewForm.userName }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>用户手机号：</label>
                <span>{{ viewForm.userPhone }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 报名表单数据 -->
        <div class="form-data">
          <h3>报名表单数据</h3>
          <el-table :data="parsedFormData" border style="width: 100%">
            <el-table-column prop="label" label="字段名称" width="180" />
            <el-table-column prop="type" label="字段类型" width="100" align="center">
              <template slot-scope="scope">
                <el-tag size="mini" :type="getFieldTypeTagType(scope.row.type)">
                  {{ getFieldTypeLabel(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="填写内容" min-width="400">
              <template slot-scope="scope">
                <!-- 文件类型显示 -->
                <div v-if="scope.row.type === 'file'" class="file-display">
                  <div v-if="scope.row.files && scope.row.files.length > 0">
                    <div v-for="(file, index) in scope.row.files" :key="index" class="file-item">
                      <i class="el-icon-document"></i>
                      <a :href="file.url" target="_blank" class="file-link">{{ file.name }}</a>
                    </div>
                  </div>
                  <span v-else class="empty-value">未上传文件</span>
                </div>
                <!-- 多选类型显示（数组） -->
                <div v-else-if="Array.isArray(scope.row.value) && scope.row.value.length > 0" class="array-display">
                  <el-tag v-for="(item, index) in scope.row.value" :key="index" size="small" style="margin-right: 5px;">
                    {{ item }}
                  </el-tag>
                </div>
                <!-- 多行文本显示 -->
                <div v-else-if="scope.row.type === 'textarea' && scope.row.value" class="textarea-display">
                  <div class="textarea-content">{{ scope.row.value }}</div>
                </div>
                <!-- 选择类型显示（带标签） -->
                <div v-else-if="['radio', 'select', 'picker', 'select_other', 'radio_other'].includes(scope.row.type) && scope.row.value" class="select-display">
                  <el-tag size="small" type="success">{{ scope.row.value }}</el-tag>
                </div>
                <!-- 日期类型显示 -->
                <div v-else-if="scope.row.type === 'date' && scope.row.value" class="date-display">
                  <i class="el-icon-date" style="margin-right: 5px;"></i>
                  <span>{{ scope.row.value }}</span>
                </div>
                <!-- 普通文本显示 -->
                <span v-else-if="scope.row.value !== null && scope.row.value !== undefined && scope.row.value !== ''" class="text-value">{{ scope.row.value }}</span>
                <!-- 空值显示 -->
                <span v-else class="empty-value">
                  <i class="el-icon-minus" style="margin-right: 3px;"></i>未填写
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 原始数据（可折叠） -->
        <div class="raw-data">
          <el-collapse>
            <el-collapse-item title="查看原始JSON数据" name="rawData">
              <el-input v-model="viewForm.formData" type="textarea" :rows="8" readonly />
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewOpen = false">关 闭</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { listProjectRegistration, getProjectRegistration, delProjectRegistration } from "@/api/miniapp/haitang/projectRegistration";

export default {
  name: "ProjectRegistration",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 天大海棠杯项目报名记录表格数据
      registrationList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: null,
        userPhone: null
      },
      // 查看表单参数
      viewForm: {},
      // 是否显示查看弹出层
      viewOpen: false
    };
  },
  computed: {
    /** 解析后的表单数据 */
    parsedFormData() {
      if (!this.viewForm.formData) return [];

      try {
        const formData = JSON.parse(this.viewForm.formData);

        // 检查数据格式：如果是数组格式（新格式），直接处理
        if (Array.isArray(formData)) {
          return formData.map(field => ({
            name: field.name,
            label: field.label || this.getDefaultFieldLabel(field.name) || field.name,
            value: this.formatFieldValue(field.value, field.type),
            type: field.type || 'input',
            files: field.type === 'file' ? this.parseFileValue(field.value) : null,
            required: field.required || false,
            options: field.options || ''
          }));
        }

        // 兼容旧格式：对象格式的数据
        const configData = this.viewForm.configData ? JSON.parse(this.viewForm.configData) : [];

        // 创建字段配置映射
        const fieldConfigMap = {};
        configData.forEach(field => {
          fieldConfigMap[field.name] = field;
        });

        // 解析表单数据
        const parsedData = [];
        for (const [fieldName, fieldValue] of Object.entries(formData)) {
          const fieldConfig = fieldConfigMap[fieldName] || {};

          // 优先使用配置中的中文标签，如果没有则使用字段名
          const displayLabel = fieldConfig.label || this.getDefaultFieldLabel(fieldName) || fieldName;

          parsedData.push({
            name: fieldName,
            label: displayLabel,
            value: this.formatFieldValue(fieldValue, fieldConfig.type),
            type: fieldConfig.type || 'input',
            files: fieldConfig.type === 'file' ? this.parseFileValue(fieldValue) : null
          });
        }

        return parsedData;
      } catch (e) {
        console.error('解析表单数据失败:', e);
        return [];
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询天大海棠杯项目报名记录列表 */
    getList() {
      this.loading = true;
      listProjectRegistration(this.queryParams).then(response => {
        this.registrationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.registrationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate() {
      // 这里可以添加修改逻辑，暂时不实现
      this.$modal.msgInfo("修改功能暂未实现");
    },
    /** 查看按钮操作 */
    handleView(row) {
      getProjectRegistration(row.registrationId).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const registrationIds = row.registrationId || this.ids;
      this.$modal.confirm('是否确认删除天大海棠杯项目报名记录编号为"' + registrationIds + '"的数据项？').then(function() {
        return delProjectRegistration(registrationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('miniapp/haitang/registration/export', {
        ...this.queryParams
      }, `projectRegistration_${new Date().getTime()}.xlsx`)
    },
    /** 格式化字段值 */
    formatFieldValue(value, fieldType) {
      // 处理空值
      if (value === null || value === undefined || value === '') {
        return '';
      }

      switch (fieldType) {
        case 'checkbox':
        case 'checkbox_other':
          // 多选值通常是数组
          if (Array.isArray(value)) {
            return value.filter(v => v !== null && v !== undefined && v !== '');
          } else if (typeof value === 'string') {
            // 如果是字符串，尝试按逗号分割
            return value.split(',').map(v => v.trim()).filter(v => v !== '');
          }
          return [value];
        case 'radio':
        case 'radio_other':
        case 'select':
        case 'select_other':
        case 'picker':
          // 单选值，处理空字符串的情况
          return value && value !== '' ? String(value) : '';
        case 'file':
          // 文件类型特殊处理
          return value ? '查看文件' : '';
        case 'date':
          // 日期格式化
          if (value && value !== '') {
            // 如果是标准日期格式，直接返回
            if (/^\d{4}-\d{2}-\d{2}$/.test(value)) {
              return value;
            }
            // 否则尝试格式化
            return this.parseTime ? this.parseTime(value, '{y}-{m}-{d}') : value;
          }
          return '';
        case 'datetime':
          // 日期时间格式化
          if (value && value !== '') {
            return this.parseTime ? this.parseTime(value, '{y}-{m}-{d} {h}:{i}:{s}') : value;
          }
          return '';
        case 'number':
          // 数字格式化
          return value ? String(value) : '';
        case 'textarea':
          // 多行文本
          return String(value);
        case 'input':
        default:
          return String(value);
      }
    },
    /** 解析文件值 */
    parseFileValue(value) {
      if (!value) return [];

      try {
        if (typeof value === 'string') {
          // 检查是否是URL格式（以http开头）
          if (value.startsWith('http://') || value.startsWith('https://')) {
            // 从URL中提取文件名
            const fileName = value.split('/').pop() || '文件';
            return [{ name: fileName, url: value }];
          }

          // 尝试解析JSON字符串
          try {
            const parsed = JSON.parse(value);
            return Array.isArray(parsed) ? parsed : [parsed];
          } catch (jsonError) {
            // 如果不是JSON，当作普通文件名处理
            return [{ name: value, url: value }];
          }
        } else if (Array.isArray(value)) {
          return value;
        } else {
          return [value];
        }
      } catch (e) {
        // 如果解析失败，当作普通字符串处理
        return [{ name: value, url: value }];
      }
    },

    /** 获取默认字段标签 */
    getDefaultFieldLabel(fieldName) {
      const labelMap = {
        // 项目相关
        'projectName': '项目名称',
        'projectDesc': '项目简介',
        'projectDescription': '项目描述',
        'projectType': '项目类型',
        'projectStage': '项目阶段',
        'projectCategory': '项目分类',

        // 团队相关
        'teamLeader': '团队负责人',
        'teamSize': '团队规模',
        'teamMember': '团队成员',
        'teamDescription': '团队介绍',

        // 联系信息
        'name': '姓名',
        'userName': '用户姓名',
        'phone': '联系电话',
        'userPhone': '用户手机号',
        'email': '邮箱地址',
        'userEmail': '用户邮箱',
        'address': '联系地址',
        'company': '所在公司',
        'position': '职位',

        // 身份信息
        'idcard': '身份证号',
        'studentId': '学号',
        'workId': '工号',

        // 时间相关
        'registrationDate': '报名日期',
        'registrationTime': '报名时间',
        'startDate': '开始日期',
        'endDate': '结束日期',
        'birthDate': '出生日期',

        // 文件相关
        'planFile': '项目计划书',
        'videoFile': '演示视频',
        'resumeFile': '个人简历',
        'certificateFile': '证书文件',
        'attachmentFile': '附件文件',

        // 选择相关
        'gender': '性别',
        'education': '学历',
        'experience': '工作经验',
        'skill': '技能',
        'interest': '兴趣爱好',
        'hobby': '爱好',

        // 业务相关
        'industry': '所属行业',
        'fundingRound': '融资轮次',
        'investment': '投资金额',
        'revenue': '营收情况',
        'website': '网站地址',
        'socialMedia': '社交媒体',

        // 其他
        'remark': '备注',
        'comment': '评论',
        'feedback': '反馈',
        'suggestion': '建议',
        'reason': '原因',
        'purpose': '目的',
        'goal': '目标',
        'plan': '计划',
        'budget': '预算',
        'requirement': '需求',
        'expectation': '期望'
      };

      return labelMap[fieldName] || null;
    },

    /** 获取字段类型标签 */
    getFieldTypeLabel(fieldType) {
      const typeLabels = {
        'input': '文本',
        'textarea': '多行文本',
        'radio': '单选',
        'radio_other': '单选+其他',
        'checkbox': '多选',
        'checkbox_other': '多选+其他',
        'select': '下拉选择',
        'select_other': '下拉+其他',
        'picker': '滚动选择',
        'date': '日期',
        'datetime': '日期时间',
        'time': '时间',
        'number': '数字',
        'file': '文件',
        'image': '图片'
      };
      return typeLabels[fieldType] || fieldType;
    },

    /** 获取字段类型标签颜色 */
    getFieldTypeTagType(fieldType) {
      const typeColors = {
        'input': '',
        'textarea': 'info',
        'radio': 'success',
        'radio_other': 'success',
        'checkbox': 'warning',
        'checkbox_other': 'warning',
        'select': 'primary',
        'select_other': 'primary',
        'picker': 'primary',
        'date': 'info',
        'datetime': 'info',
        'time': 'info',
        'number': 'success',
        'file': 'danger',
        'image': 'danger'
      };
      return typeColors[fieldType] || '';
    }
  }
};
</script>

<style scoped>
.registration-detail {
  padding: 20px;
}

.basic-info {
  margin-bottom: 30px;
}

.basic-info h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.info-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  margin-right: 10px;
  min-width: 80px;
}

.info-item span {
  color: #303133;
}

.form-data {
  margin-bottom: 30px;
}

.form-data h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 2px solid #67c23a;
  padding-bottom: 10px;
}

.file-display {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.file-link {
  color: #409eff;
  text-decoration: none;
}

.file-link:hover {
  text-decoration: underline;
}

.array-display {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.text-value {
  color: #303133;
  word-break: break-all;
  line-height: 1.5;
}

.empty-value {
  color: #c0c4cc;
  font-style: italic;
}

.textarea-display {
  max-width: 100%;
}

.textarea-content {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 120px;
  overflow-y: auto;
  font-size: 13px;
  line-height: 1.5;
}

.select-display {
  display: flex;
  align-items: center;
}

.date-display {
  display: flex;
  align-items: center;
  color: #606266;
}

.raw-data {
  margin-top: 20px;
}

.raw-data .el-collapse {
  border: none;
}

.raw-data .el-collapse-item__header {
  background: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 0 15px;
  font-size: 14px;
  color: #606266;
}

.raw-data .el-collapse-item__content {
  padding: 15px 0 0 0;
}
</style>
