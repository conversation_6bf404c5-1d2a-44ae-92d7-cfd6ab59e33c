{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-c6c2611e\"],{\"0850\":function(e,t,a){},\"3c34\":function(e,t,a){\"use strict\";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a(\"div\",{staticClass:\"app-container\"},[a(\"el-form\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.showSearch,expression:\"showSearch\"}],ref:\"queryForm\",attrs:{model:e.queryParams,size:\"small\",inline:!0,\"label-width\":\"68px\"}},[a(\"el-form-item\",{attrs:{label:\"用户姓名\",prop:\"userName\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入用户姓名\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.userName,callback:function(t){e.$set(e.queryParams,\"userName\",t)},expression:\"queryParams.userName\"}})],1),a(\"el-form-item\",{attrs:{label:\"用户手机号\",prop:\"userPhone\"}},[a(\"el-input\",{attrs:{placeholder:\"请输入用户手机号\",clearable:\"\"},nativeOn:{keyup:function(t){return!t.type.indexOf(\"key\")&&e._k(t.keyCode,\"enter\",13,t.key,\"Enter\")?null:e.handleQuery(t)}},model:{value:e.queryParams.userPhone,callback:function(t){e.$set(e.queryParams,\"userPhone\",t)},expression:\"queryParams.userPhone\"}})],1),a(\"el-form-item\",[a(\"el-button\",{attrs:{type:\"primary\",icon:\"el-icon-search\",size:\"mini\"},on:{click:e.handleQuery}},[e._v(\"搜索\")]),a(\"el-button\",{attrs:{icon:\"el-icon-refresh\",size:\"mini\"},on:{click:e.resetQuery}},[e._v(\"重置\")])],1)],1),a(\"el-row\",{staticClass:\"mb8\",attrs:{gutter:10}},[a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:haitang:registration:edit\"],expression:\"['miniapp:haitang:registration:edit']\"}],attrs:{type:\"success\",plain:\"\",icon:\"el-icon-edit\",size:\"mini\",disabled:e.single},on:{click:e.handleUpdate}},[e._v(\"修改\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:haitang:registration:remove\"],expression:\"['miniapp:haitang:registration:remove']\"}],attrs:{type:\"danger\",plain:\"\",icon:\"el-icon-delete\",size:\"mini\",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(\"删除\")])],1),a(\"el-col\",{attrs:{span:1.5}},[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:haitang:registration:export\"],expression:\"['miniapp:haitang:registration:export']\"}],attrs:{type:\"warning\",plain:\"\",icon:\"el-icon-download\",size:\"mini\"},on:{click:e.handleExport}},[e._v(\"导出\")])],1),a(\"right-toolbar\",{attrs:{showSearch:e.showSearch},on:{\"update:showSearch\":function(t){e.showSearch=t},\"update:show-search\":function(t){e.showSearch=t},queryTable:e.getList}})],1),a(\"el-table\",{directives:[{name:\"loading\",rawName:\"v-loading\",value:e.loading,expression:\"loading\"}],attrs:{data:e.registrationList},on:{\"selection-change\":e.handleSelectionChange}},[a(\"el-table-column\",{attrs:{type:\"selection\",width:\"55\",align:\"center\"}}),a(\"el-table-column\",{attrs:{label:\"报名ID\",align:\"center\",prop:\"registrationId\"}}),a(\"el-table-column\",{attrs:{label:\"表单配置\",align:\"center\",prop:\"configName\"}}),a(\"el-table-column\",{attrs:{label:\"用户姓名\",align:\"center\",prop:\"userName\"}}),a(\"el-table-column\",{attrs:{label:\"用户手机号\",align:\"center\",prop:\"userPhone\"}}),a(\"el-table-column\",{attrs:{label:\"报名时间\",align:\"center\",prop:\"registrationTime\",width:\"180\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"span\",[e._v(e._s(e.parseTime(t.row.registrationTime,\"{y}-{m}-{d} {h}:{i}:{s}\")))])]}}])}),a(\"el-table-column\",{attrs:{label:\"操作\",align:\"center\",\"class-name\":\"small-padding fixed-width\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:haitang:registration:query\"],expression:\"['miniapp:haitang:registration:query']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-view\"},on:{click:function(a){return e.handleView(t.row)}}},[e._v(\"查看\")]),a(\"el-button\",{directives:[{name:\"hasPermi\",rawName:\"v-hasPermi\",value:[\"miniapp:haitang:registration:remove\"],expression:\"['miniapp:haitang:registration:remove']\"}],attrs:{size:\"mini\",type:\"text\",icon:\"el-icon-delete\"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(\"删除\")])]}}])})],1),a(\"pagination\",{directives:[{name:\"show\",rawName:\"v-show\",value:e.total>0,expression:\"total>0\"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{\"update:page\":function(t){return e.$set(e.queryParams,\"pageNum\",t)},\"update:limit\":function(t){return e.$set(e.queryParams,\"pageSize\",t)},pagination:e.getList}}),a(\"el-dialog\",{attrs:{title:\"报名详情\",visible:e.viewOpen,width:\"900px\",\"append-to-body\":\"\"},on:{\"update:visible\":function(t){e.viewOpen=t}}},[a(\"div\",{staticClass:\"registration-detail\"},[a(\"div\",{staticClass:\"basic-info\"},[a(\"h3\",[e._v(\"基本信息\")]),a(\"el-row\",{attrs:{gutter:20}},[a(\"el-col\",{attrs:{span:12}},[a(\"div\",{staticClass:\"info-item\"},[a(\"label\",[e._v(\"表单配置：\")]),a(\"span\",[e._v(e._s(e.viewForm.configName))])])]),a(\"el-col\",{attrs:{span:12}},[a(\"div\",{staticClass:\"info-item\"},[a(\"label\",[e._v(\"报名时间：\")]),a(\"span\",[e._v(e._s(e.parseTime(e.viewForm.registrationTime,\"{y}-{m}-{d} {h}:{i}:{s}\")))])])]),a(\"el-col\",{attrs:{span:12}},[a(\"div\",{staticClass:\"info-item\"},[a(\"label\",[e._v(\"用户姓名：\")]),a(\"span\",[e._v(e._s(e.viewForm.userName))])])]),a(\"el-col\",{attrs:{span:12}},[a(\"div\",{staticClass:\"info-item\"},[a(\"label\",[e._v(\"用户手机号：\")]),a(\"span\",[e._v(e._s(e.viewForm.userPhone))])])])],1)],1),a(\"div\",{staticClass:\"form-data\"},[a(\"h3\",[e._v(\"报名表单数据\")]),a(\"el-table\",{staticStyle:{width:\"100%\"},attrs:{data:e.parsedFormData,border:\"\"}},[a(\"el-table-column\",{attrs:{prop:\"label\",label:\"字段名称\",width:\"180\"}}),a(\"el-table-column\",{attrs:{prop:\"type\",label:\"字段类型\",width:\"100\",align:\"center\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[a(\"el-tag\",{attrs:{size:\"mini\",type:e.getFieldTypeTagType(t.row.type)}},[e._v(\" \"+e._s(e.getFieldTypeLabel(t.row.type))+\" \")])]}}])}),a(\"el-table-column\",{attrs:{prop:\"value\",label:\"填写内容\",\"min-width\":\"400\"},scopedSlots:e._u([{key:\"default\",fn:function(t){return[\"file\"===t.row.type?a(\"div\",{staticClass:\"file-display\"},[t.row.files&&t.row.files.length>0?a(\"div\",e._l(t.row.files,(function(t,i){return a(\"div\",{key:i,staticClass:\"file-item\"},[a(\"i\",{staticClass:\"el-icon-document\"}),a(\"a\",{staticClass:\"file-link\",attrs:{href:t.url,target:\"_blank\"}},[e._v(e._s(t.name))])])})),0):a(\"span\",{staticClass:\"empty-value\"},[e._v(\"未上传文件\")])]):Array.isArray(t.row.value)&&t.row.value.length>0?a(\"div\",{staticClass:\"array-display\"},e._l(t.row.value,(function(t,i){return a(\"el-tag\",{key:i,staticStyle:{\"margin-right\":\"5px\"},attrs:{size:\"small\"}},[e._v(\" \"+e._s(t)+\" \")])})),1):\"textarea\"===t.row.type&&t.row.value?a(\"div\",{staticClass:\"textarea-display\"},[a(\"div\",{staticClass:\"textarea-content\"},[e._v(e._s(t.row.value))])]):[\"radio\",\"select\",\"picker\",\"select_other\",\"radio_other\"].includes(t.row.type)&&t.row.value?a(\"div\",{staticClass:\"select-display\"},[a(\"el-tag\",{attrs:{size:\"small\",type:\"success\"}},[e._v(e._s(t.row.value))])],1):\"date\"===t.row.type&&t.row.value?a(\"div\",{staticClass:\"date-display\"},[a(\"i\",{staticClass:\"el-icon-date\",staticStyle:{\"margin-right\":\"5px\"}}),a(\"span\",[e._v(e._s(t.row.value))])]):null!==t.row.value&&void 0!==t.row.value&&\"\"!==t.row.value?a(\"span\",{staticClass:\"text-value\"},[e._v(e._s(t.row.value))]):a(\"span\",{staticClass:\"empty-value\"},[a(\"i\",{staticClass:\"el-icon-minus\",staticStyle:{\"margin-right\":\"3px\"}}),e._v(\"未填写 \")])]}}])})],1)],1),a(\"div\",{staticClass:\"raw-data\"},[a(\"el-collapse\",[a(\"el-collapse-item\",{attrs:{title:\"查看原始JSON数据\",name:\"rawData\"}},[a(\"el-input\",{attrs:{type:\"textarea\",rows:8,readonly:\"\"},model:{value:e.viewForm.formData,callback:function(t){e.$set(e.viewForm,\"formData\",t)},expression:\"viewForm.formData\"}})],1)],1)],1)]),a(\"div\",{staticClass:\"dialog-footer\",attrs:{slot:\"footer\"},slot:\"footer\"},[a(\"el-button\",{on:{click:function(t){e.viewOpen=!1}}},[e._v(\"关 闭\")])],1)])],1)},r=[],n=a(\"5530\"),s=a(\"3835\"),l=(a(\"4de4\"),a(\"d81d\"),a(\"14d9\"),a(\"b0c0\"),a(\"4fad\"),a(\"b64b\"),a(\"d3b7\"),a(\"ac1f\"),a(\"00b4\"),a(\"2ca0\"),a(\"498a\"),a(\"0643\"),a(\"2382\"),a(\"4e3e\"),a(\"a573\"),a(\"159b\"),a(\"b775\"));function o(e){return Object(l[\"a\"])({url:\"/miniapp/haitang/registration/list\",method:\"get\",params:e})}function c(e){return Object(l[\"a\"])({url:\"/miniapp/haitang/registration/\"+e,method:\"get\"})}function u(e){return Object(l[\"a\"])({url:\"/miniapp/haitang/registration/\"+e,method:\"delete\"})}var p={name:\"ProjectRegistration\",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,registrationList:[],queryParams:{pageNum:1,pageSize:10,userName:null,userPhone:null},viewForm:{},viewOpen:!1}},computed:{parsedFormData:function(){var e=this;if(!this.viewForm.formData)return[];try{var t=JSON.parse(this.viewForm.formData);if(Array.isArray(t))return t.map((function(t){return{name:t.name,label:t.label||e.getDefaultFieldLabel(t.name)||t.name,value:e.formatFieldValue(t.value,t.type),type:t.type||\"input\",files:\"file\"===t.type?e.parseFileValue(t.value):null,required:t.required||!1,options:t.options||\"\"}}));var a=this.viewForm.configData?JSON.parse(this.viewForm.configData):[],i={};a.forEach((function(e){i[e.name]=e}));for(var r=[],n=0,l=Object.entries(t);n<l.length;n++){var o=Object(s[\"a\"])(l[n],2),c=o[0],u=o[1],p=i[c]||{},m=p.label||this.getDefaultFieldLabel(c)||c;r.push({name:c,label:m,value:this.formatFieldValue(u,p.type),type:p.type||\"input\",files:\"file\"===p.type?this.parseFileValue(u):null})}return r}catch(d){return console.error(\"解析表单数据失败:\",d),[]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.registrationList=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm(\"queryForm\"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.registrationId})),this.single=1!==e.length,this.multiple=!e.length},handleUpdate:function(){this.$modal.msgInfo(\"修改功能暂未实现\")},handleView:function(e){var t=this;c(e.registrationId).then((function(e){t.viewForm=e.data,t.viewOpen=!0}))},handleDelete:function(e){var t=this,a=e.registrationId||this.ids;this.$modal.confirm('是否确认删除天大海棠杯项目报名记录编号为\"'+a+'\"的数据项？').then((function(){return u(a)})).then((function(){t.getList(),t.$modal.msgSuccess(\"删除成功\")})).catch((function(){}))},handleExport:function(){this.download(\"miniapp/haitang/registration/export\",Object(n[\"a\"])({},this.queryParams),\"projectRegistration_\".concat((new Date).getTime(),\".xlsx\"))},formatFieldValue:function(e,t){if(null===e||void 0===e||\"\"===e)return\"\";switch(t){case\"checkbox\":case\"checkbox_other\":return Array.isArray(e)?e.filter((function(e){return null!==e&&void 0!==e&&\"\"!==e})):\"string\"===typeof e?e.split(\",\").map((function(e){return e.trim()})).filter((function(e){return\"\"!==e})):[e];case\"radio\":case\"radio_other\":case\"select\":case\"select_other\":case\"picker\":return e&&\"\"!==e?String(e):\"\";case\"file\":return e?\"查看文件\":\"\";case\"date\":return e&&\"\"!==e?/^\\d{4}-\\d{2}-\\d{2}$/.test(e)?e:this.parseTime?this.parseTime(e,\"{y}-{m}-{d}\"):e:\"\";case\"datetime\":return e&&\"\"!==e?this.parseTime?this.parseTime(e,\"{y}-{m}-{d} {h}:{i}:{s}\"):e:\"\";case\"number\":return e?String(e):\"\";case\"textarea\":return String(e);case\"input\":default:return String(e)}},parseFileValue:function(e){if(!e)return[];try{if(\"string\"!==typeof e)return Array.isArray(e)?e:[e];if(e.startsWith(\"http://\")||e.startsWith(\"https://\")){var t=e.split(\"/\").pop()||\"文件\";return[{name:t,url:e}]}try{var a=JSON.parse(e);return Array.isArray(a)?a:[a]}catch(i){return[{name:e,url:e}]}}catch(r){return[{name:e,url:e}]}},getDefaultFieldLabel:function(e){var t={projectName:\"项目名称\",projectDesc:\"项目简介\",projectDescription:\"项目描述\",projectType:\"项目类型\",projectStage:\"项目阶段\",projectCategory:\"项目分类\",teamLeader:\"团队负责人\",teamSize:\"团队规模\",teamMember:\"团队成员\",teamDescription:\"团队介绍\",name:\"姓名\",userName:\"用户姓名\",phone:\"联系电话\",userPhone:\"用户手机号\",email:\"邮箱地址\",userEmail:\"用户邮箱\",address:\"联系地址\",company:\"所在公司\",position:\"职位\",idcard:\"身份证号\",studentId:\"学号\",workId:\"工号\",registrationDate:\"报名日期\",registrationTime:\"报名时间\",startDate:\"开始日期\",endDate:\"结束日期\",birthDate:\"出生日期\",planFile:\"项目计划书\",videoFile:\"演示视频\",resumeFile:\"个人简历\",certificateFile:\"证书文件\",attachmentFile:\"附件文件\",gender:\"性别\",education:\"学历\",experience:\"工作经验\",skill:\"技能\",interest:\"兴趣爱好\",hobby:\"爱好\",industry:\"所属行业\",fundingRound:\"融资轮次\",investment:\"投资金额\",revenue:\"营收情况\",website:\"网站地址\",socialMedia:\"社交媒体\",remark:\"备注\",comment:\"评论\",feedback:\"反馈\",suggestion:\"建议\",reason:\"原因\",purpose:\"目的\",goal:\"目标\",plan:\"计划\",budget:\"预算\",requirement:\"需求\",expectation:\"期望\"};return t[e]||null},getFieldTypeLabel:function(e){var t={input:\"文本\",textarea:\"多行文本\",radio:\"单选\",radio_other:\"单选+其他\",checkbox:\"多选\",checkbox_other:\"多选+其他\",select:\"下拉选择\",select_other:\"下拉+其他\",picker:\"滚动选择\",date:\"日期\",datetime:\"日期时间\",time:\"时间\",number:\"数字\",file:\"文件\",image:\"图片\"};return t[e]||e},getFieldTypeTagType:function(e){var t={input:\"\",textarea:\"info\",radio:\"success\",radio_other:\"success\",checkbox:\"warning\",checkbox_other:\"warning\",select:\"primary\",select_other:\"primary\",picker:\"primary\",date:\"info\",datetime:\"info\",time:\"info\",number:\"success\",file:\"danger\",image:\"danger\"};return t[e]||\"\"}}},m=p,d=(a(\"7c75\"),a(\"2877\")),h=Object(d[\"a\"])(m,i,r,!1,null,\"72d15aec\",null);t[\"default\"]=h.exports},\"7c75\":function(e,t,a){\"use strict\";a(\"0850\")}}]);", "extractedComments": []}