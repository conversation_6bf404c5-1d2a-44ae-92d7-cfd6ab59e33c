package com.ruoyi.miniapp.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.miniapp.mapper.MiniHaitangProjectConfigMapper;
import com.ruoyi.miniapp.service.IMiniHaitangProjectConfigService;

/**
 * 天大海棠杯项目配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-05
 */
@Service
public class MiniHaitangProjectConfigServiceImpl implements IMiniHaitangProjectConfigService 
{
    @Autowired
    private MiniHaitangProjectConfigMapper miniHaitangProjectConfigMapper;

    /**
     * 获取赞助商图片
     * 
     * @return 赞助商图片URL
     */
    @Override
    public String getSponsorUnit()
    {
        return miniHaitangProjectConfigMapper.selectSponsorUnit();
    }

    /**
     * 更新赞助商图片
     * 
     * @param sponsorUnit 赞助商图片URL
     * @return 结果
     */
    @Override
    public boolean updateSponsorUnit(String sponsorUnit)
    {
        return miniHaitangProjectConfigMapper.updateSponsorUnit(sponsorUnit) > 0;
    }
}
