{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectFormConfig\\index.vue", "mtime": 1754384002001}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Rm9ybUNvbmZpZywgZ2V0Rm9ybUNvbmZpZywgZGVsRm9ybUNvbmZpZywgYWRkRm9ybUNvbmZpZywgdXBkYXRlRm9ybUNvbmZpZywgZW5hYmxlRm9ybUNvbmZpZywgZ2V0U3BvbnNvckltYWdlLCB1cGRhdGVTcG9uc29ySW1hZ2UgfSBmcm9tICJAL2FwaS9taW5pYXBwL2hhaXRhbmcvZm9ybUNvbmZpZyI7DQppbXBvcnQgcmVxdWVzdCBmcm9tICdAL3V0aWxzL3JlcXVlc3QnOw0KaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJGb3JtQ29uZmlnIiwNCiAgZGljdHM6IFsnc3lzX25vcm1hbF9kaXNhYmxlJ10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIGxpc3RMb2FkaW5nOiBmYWxzZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva7ooajmoLzmlbDmja4NCiAgICAgIGZvcm1Db25maWdMaXN0OiBbXSwNCiAgICAgIC8vIOW9k+WJjeWQr+eUqOeahOmFjee9rg0KICAgICAgZW5hYmxlZENvbmZpZzogbnVsbCwNCiAgICAgIC8vIOWQr+eUqOmFjee9rueahOihqOWNleWtl+autQ0KICAgICAgZW5hYmxlZEZvcm1GaWVsZHM6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5piv5ZCm5pi+56S66KGo5Y2V6YWN572u5by55Ye65bGCDQogICAgICBmb3JtQ29uZmlnT3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKbmmL7npLrooajljZXpooTop4jlvLnlh7rlsYINCiAgICAgIHByZXZpZXdEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIC8vIOW9k+WJjemFjee9rueahOihqOWNleWtl+autQ0KICAgICAgY3VycmVudEZvcm1GaWVsZHM6IFtdLA0KICAgICAgLy8g5b2T5YmN5pON5L2c55qE6YWN572uDQogICAgICBjdXJyZW50Q29uZmlnOiBudWxsLA0KICAgICAgLy8g5rua5Yqo5Y2V6YCJ5a2X5q6155qE57yW6L6R5pWw5o2u57yT5a2YDQogICAgICBwaWNrZXJFZGl0RGF0YToge30sDQogICAgICAvLyDotZ7liqnllYblm77niYfnm7jlhbMNCiAgICAgIHNwb25zb3JEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRTcG9uc29ySW1hZ2U6ICcnLA0KICAgICAgdXBsb2FkQWN0aW9uOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9jb21tb24vdXBsb2FkIiwNCiAgICAgIHVwbG9hZEhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKSB9LA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGNvbmZpZ05hbWU6IG51bGwsDQogICAgICAgIHN0YXR1czogbnVsbA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGNvbmZpZ05hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6YWN572u5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5sb2FkRW5hYmxlZENvbmZpZygpOw0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOWKoOi9veWQr+eUqOeahOmFjee9riAqLw0KICAgIGxvYWRFbmFibGVkQ29uZmlnKCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIHJlcXVlc3Qoew0KICAgICAgICB1cmw6ICcvbWluaWFwcC9oYWl0YW5nL2Zvcm1Db25maWcvZW5hYmxlZCcsDQogICAgICAgIG1ldGhvZDogJ2dldCcNCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIHRoaXMuZW5hYmxlZENvbmZpZyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgICAgdGhpcy5sb2FkRW5hYmxlZEZvcm1GaWVsZHMoKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmVuYWJsZWRDb25maWcgPSBudWxsOw0KICAgICAgICAgIHRoaXMuZW5hYmxlZEZvcm1GaWVsZHMgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy5lbmFibGVkQ29uZmlnID0gbnVsbDsNCiAgICAgICAgdGhpcy5lbmFibGVkRm9ybUZpZWxkcyA9IFtdOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWKoOi9veWQr+eUqOmFjee9rueahOihqOWNleWtl+autSAqLw0KICAgIGxvYWRFbmFibGVkRm9ybUZpZWxkcygpIHsNCiAgICAgIGlmICh0aGlzLmVuYWJsZWRDb25maWcgJiYgdGhpcy5lbmFibGVkQ29uZmlnLmZvcm1Db25maWcpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmVuYWJsZWRGb3JtRmllbGRzID0gSlNPTi5wYXJzZSh0aGlzLmVuYWJsZWRDb25maWcuZm9ybUNvbmZpZyk7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmVuYWJsZWRGb3JtRmllbGRzID0gW107DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZW5hYmxlZEZvcm1GaWVsZHMgPSBbXTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmn6Xor6LlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva7liJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5saXN0TG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0Rm9ybUNvbmZpZyh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtQ29uZmlnTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5saXN0TG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBjb25maWdJZDogbnVsbCwNCiAgICAgICAgY29uZmlnTmFtZTogbnVsbCwNCiAgICAgICAgY29uZmlnRGVzY3JpcHRpb246IG51bGwsDQogICAgICAgIGZvcm1Db25maWc6IG51bGwsDQogICAgICAgIGlzRW5hYmxlZDogIjAiLA0KICAgICAgICBzb3J0T3JkZXI6IDAsDQogICAgICAgIHN0YXR1czogIjAiLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIHJlbWFyazogbnVsbA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uY29uZmlnSWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva4iOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IGNvbmZpZ0lkID0gcm93LmNvbmZpZ0lkIHx8IHRoaXMuaWRzDQogICAgICBnZXRGb3JtQ29uZmlnKGNvbmZpZ0lkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva4iOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uY29uZmlnSWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlRm9ybUNvbmZpZyh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZEZvcm1Db25maWcodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgY29uZmlnSWRzID0gcm93LmNvbmZpZ0lkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5aSp5aSn5rW35qOg5p2v6aG555uu5oql5ZCN6KGo5Y2V6YWN572u57yW5Y+35Li6IicgKyBjb25maWdJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxGb3JtQ29uZmlnKGNvbmZpZ0lkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyoqIOihqOWNlemFjee9ruaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUZvcm1Db25maWcocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRDb25maWcgPSByb3c7DQogICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gW107DQogICAgICB0aGlzLnBpY2tlckVkaXREYXRhID0ge307IC8vIOa4heepuue8lui+keaVsOaNrue8k+WtmA0KDQogICAgICBpZiAocm93LmZvcm1Db25maWcpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gSlNPTi5wYXJzZShyb3cuZm9ybUNvbmZpZyk7DQogICAgICAgICAgLy8g5Yid5aeL5YyW5rua5Yqo5Y2V6YCJ5a2X5q6155qE57yW6L6R5pWw5o2uDQogICAgICAgICAgdGhpcy5jdXJyZW50Rm9ybUZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICAgIGlmIChmaWVsZC50eXBlID09PSAncGlja2VyJyAmJiBmaWVsZC5vcHRpb25zKSB7DQogICAgICAgICAgICAgIGNvbnN0IGZpZWxkS2V5ID0gZmllbGQubmFtZSB8fCAndGVtcF8nICsgRGF0ZS5ub3coKTsNCiAgICAgICAgICAgICAgdGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV0gPSB0aGlzLmdldFBpY2tlck9wdGlvbnMoZmllbGQub3B0aW9ucyk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gW107DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybUNvbmZpZ09wZW4gPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOmihOiniOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVByZXZpZXcocm93KSB7DQogICAgICB0aGlzLmN1cnJlbnRDb25maWcgPSByb3c7DQogICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gW107DQogICAgICBpZiAocm93LmZvcm1Db25maWcpIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gSlNPTi5wYXJzZShyb3cuZm9ybUNvbmZpZyk7DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzID0gW107DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMucHJldmlld0RpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOWQr+eUqOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUVuYWJsZShyb3cpIHsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+WQr+eUqOatpOmFjee9ruWwhuemgeeUqOWFtuS7luaJgOaciemFjee9ru+8jOaYr+WQpuehruiupOWQr+eUqO+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBlbmFibGVGb3JtQ29uZmlnKHJvdy5jb25maWdJZCk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5sb2FkRW5hYmxlZENvbmZpZygpOw0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5ZCv55So5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5re75Yqg6KGo5Y2V5a2X5q61ICovDQogICAgYWRkRm9ybUZpZWxkKCkgew0KICAgICAgY29uc3QgZGVmYXVsdE5hbWUgPSB0aGlzLmdlbmVyYXRlVW5pcXVlRmllbGROYW1lKCdmaWVsZCcpOw0KICAgICAgY29uc3QgbmV3RmllbGQgPSB7DQogICAgICAgIG5hbWU6IGRlZmF1bHROYW1lLA0KICAgICAgICBsYWJlbDogJycsDQogICAgICAgIHR5cGU6ICdpbnB1dCcsDQogICAgICAgIHJlcXVpcmVkOiBmYWxzZSwNCiAgICAgICAgb3B0aW9uczogJycNCiAgICAgIH07DQogICAgICB0aGlzLmN1cnJlbnRGb3JtRmllbGRzLnB1c2gobmV3RmllbGQpOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOihqOWNleWtl+autSAqLw0KICAgIHJlbW92ZUZvcm1GaWVsZChpbmRleCkgew0KICAgICAgdGhpcy5jdXJyZW50Rm9ybUZpZWxkcy5zcGxpY2UoaW5kZXgsIDEpOw0KICAgIH0sDQogICAgLyoqIOeUn+aIkOWUr+S4gOWtl+auteWQjSAqLw0KICAgIGdlbmVyYXRlVW5pcXVlRmllbGROYW1lKHByZWZpeCkgew0KICAgICAgbGV0IGNvdW50ZXIgPSAxOw0KICAgICAgbGV0IG5hbWUgPSBwcmVmaXggKyBjb3VudGVyOw0KICAgICAgd2hpbGUgKHRoaXMuY3VycmVudEZvcm1GaWVsZHMuc29tZShmaWVsZCA9PiBmaWVsZC5uYW1lID09PSBuYW1lKSkgew0KICAgICAgICBjb3VudGVyKys7DQogICAgICAgIG5hbWUgPSBwcmVmaXggKyBjb3VudGVyOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIG5hbWU7DQogICAgfSwNCiAgICAvKiog5Yik5pat5a2X5q6157G75Z6L5piv5ZCm6ZyA6KaB6YCJ6aG5ICovDQogICAgbmVlZE9wdGlvbnModHlwZSkgew0KICAgICAgcmV0dXJuIFsncmFkaW8nLCAnY2hlY2tib3gnLCAnc2VsZWN0JywgJ3JhZGlvX290aGVyJywgJ2NoZWNrYm94X290aGVyJywgJ3NlbGVjdF9vdGhlcicsICdwaWNrZXInXS5pbmNsdWRlcyh0eXBlKTsNCiAgICB9LA0KICAgIC8qKiDlpITnkIbmqKHmnb/lkb3ku6QgKi8NCiAgICBoYW5kbGVUZW1wbGF0ZUNvbW1hbmQoY29tbWFuZCkgew0KICAgICAgaWYgKGNvbW1hbmQgPT09ICdjbGVhcicpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn56Gu6K6k5riF56m65omA5pyJ5a2X5q6177yfJykudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5jdXJyZW50Rm9ybUZpZWxkcyA9IFtdOw0KICAgICAgICB9KTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCB0ZW1wbGF0ZXMgPSB7DQogICAgICAgIGJhc2ljOiBbDQogICAgICAgICAgeyBsYWJlbDogJ+Wnk+WQjScsIG5hbWU6ICcnLCB0eXBlOiAnaW5wdXQnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn6IGU57O755S16K+dJywgbmFtZTogJycsIHR5cGU6ICd0ZWwnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn6YKu566x5Zyw5Z2AJywgbmFtZTogJycsIHR5cGU6ICdlbWFpbCcsIHJlcXVpcmVkOiBmYWxzZSwgb3B0aW9uczogJycgfQ0KICAgICAgICBdLA0KICAgICAgICBwcm9qZWN0OiBbDQogICAgICAgICAgeyBsYWJlbDogJ+mhueebruWQjeensCcsIG5hbWU6ICcnLCB0eXBlOiAnaW5wdXQnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5Zui6Zif6LSf6LSj5Lq6JywgbmFtZTogJycsIHR5cGU6ICdpbnB1dCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfogZTns7vnlLXor50nLCBuYW1lOiAnJywgdHlwZTogJ3RlbCcsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfpgq7nrrHlnLDlnYAnLCBuYW1lOiAnJywgdHlwZTogJ2VtYWlsJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICcnIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+mhueebrueugOS7iycsIG5hbWU6ICcnLCB0eXBlOiAndGV4dGFyZWEnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn6aG555uu57G75Z6LJywgbmFtZTogJycsIHR5cGU6ICdzZWxlY3Rfb3RoZXInLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJ+enkeaKgOWIm+aWsCzllYbkuJrmqKHlvI8s56S+5Lya5YWs55uKLOaWh+WMluWIm+aEjycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5Zui6Zif6KeE5qihJywgbmFtZTogJycsIHR5cGU6ICdyYWRpbycsIHJlcXVpcmVkOiB0cnVlLCBvcHRpb25zOiAnMeS6uiwyLTPkurosNC015Lq6LDYtMTDkurosMTDkurrku6XkuIonIH0sDQogICAgICAgICAgeyBsYWJlbDogJ+mhueebrumYtuautScsIG5hbWU6ICcnLCB0eXBlOiAncGlja2VyJywgcmVxdWlyZWQ6IHRydWUsIG9wdGlvbnM6ICdbeyJ0ZXh0Ijoi5Yib5oSP6Zi25q61IiwiZGlzYWJsZWQiOmZhbHNlfSx7InRleHQiOiLliJ3liJvpmLbmrrUiLCJkaXNhYmxlZCI6ZmFsc2V9LHsidGV4dCI6IuaIkOmVv+mYtuautSIsImRpc2FibGVkIjpmYWxzZX0seyJ0ZXh0Ijoi5oiQ54af6Zi25q61IiwiZGlzYWJsZWQiOnRydWV9XScgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5oql5ZCN5pel5pyfJywgbmFtZTogJycsIHR5cGU6ICdkYXRlJywgcmVxdWlyZWQ6IGZhbHNlLCBvcHRpb25zOiAnJyB9LA0KICAgICAgICAgIHsgbGFiZWw6ICfpobnnm67orqHliJLkuaYnLCBuYW1lOiAnJywgdHlwZTogJ2ZpbGUnLCByZXF1aXJlZDogdHJ1ZSwgb3B0aW9uczogJycgfSwNCiAgICAgICAgICB7IGxhYmVsOiAn5ryU56S66KeG6aKRJywgbmFtZTogJycsIHR5cGU6ICdmaWxlJywgcmVxdWlyZWQ6IGZhbHNlLCBvcHRpb25zOiAnJyB9DQogICAgICAgIF0NCiAgICAgIH07DQoNCiAgICAgIGlmICh0ZW1wbGF0ZXNbY29tbWFuZF0pIHsNCiAgICAgICAgdGhpcy5jdXJyZW50Rm9ybUZpZWxkcyA9IHRlbXBsYXRlc1tjb21tYW5kXS5tYXAoZmllbGQgPT4gKHsNCiAgICAgICAgICAuLi5maWVsZCwNCiAgICAgICAgICBuYW1lOiB0aGlzLmdlbmVyYXRlVW5pcXVlRmllbGROYW1lKGZpZWxkLmxhYmVsLnRvTG93ZXJDYXNlKCkpDQogICAgICAgIH0pKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDpooTop4jooajljZUgKi8NCiAgICBoYW5kbGVQcmV2aWV3Rm9ybSgpIHsNCiAgICAgIHRoaXMucHJldmlld0RpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOS/neWtmOihqOWNlemFjee9riAqLw0KICAgIHNhdmVGb3JtQ29uZmlnKCkgew0KICAgICAgaWYgKCF0aGlzLmN1cnJlbnRDb25maWcpIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuivt+WFiOmAieaLqeimgemFjee9rueahOihqOWNlSIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOmqjOivgeWtl+autemFjee9rg0KICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB0aGlzLmN1cnJlbnRGb3JtRmllbGRzLmxlbmd0aDsgaSsrKSB7DQogICAgICAgIGNvbnN0IGZpZWxkID0gdGhpcy5jdXJyZW50Rm9ybUZpZWxkc1tpXTsNCiAgICAgICAgaWYgKCFmaWVsZC5sYWJlbCkgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKGDnrKwke2kgKyAxfeS4quWtl+auteeahOagh+etvuS4jeiDveS4uuepumApOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoIWZpZWxkLm5hbWUpIHsNCiAgICAgICAgICBmaWVsZC5uYW1lID0gdGhpcy5nZW5lcmF0ZVVuaXF1ZUZpZWxkTmFtZShmaWVsZC5sYWJlbC50b0xvd2VyQ2FzZSgpKTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5uZWVkT3B0aW9ucyhmaWVsZC50eXBlKSAmJiAhZmllbGQub3B0aW9ucykgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKGDnrKwke2kgKyAxfeS4quWtl+autSIke2ZpZWxkLmxhYmVsfSLpnIDopoHorr7nva7pgInpobnlhoXlrrlgKTsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgY29uc3QgZm9ybURhdGEgPSB7DQogICAgICAgIGNvbmZpZ0lkOiB0aGlzLmN1cnJlbnRDb25maWcuY29uZmlnSWQsDQogICAgICAgIGZvcm1Db25maWc6IEpTT04uc3RyaW5naWZ5KHRoaXMuY3VycmVudEZvcm1GaWVsZHMpDQogICAgICB9Ow0KDQogICAgICB1cGRhdGVGb3JtQ29uZmlnKGZvcm1EYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi6KGo5Y2V6YWN572u5L+d5a2Y5oiQ5YqfIik7DQogICAgICAgIHRoaXMuZm9ybUNvbmZpZ09wZW4gPSBmYWxzZTsNCiAgICAgICAgdGhpcy5sb2FkRW5hYmxlZENvbmZpZygpOw0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOiOt+WPluWtl+autemAiemhuSAqLw0KICAgIGdldEZpZWxkT3B0aW9ucyhvcHRpb25zKSB7DQogICAgICBpZiAoIW9wdGlvbnMpIHJldHVybiBbXTsNCiAgICAgIHJldHVybiBvcHRpb25zLnNwbGl0KCcsJykubWFwKG9wdCA9PiBvcHQudHJpbSgpKS5maWx0ZXIob3B0ID0+IG9wdCk7DQogICAgfSwNCiAgICAvKiog6I635Y+W5rua5Yqo5Y2V6YCJ6YCJ6aG5ICovDQogICAgZ2V0UGlja2VyT3B0aW9ucyhvcHRpb25zKSB7DQogICAgICBpZiAoIW9wdGlvbnMpIHJldHVybiBbXTsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2Uob3B0aW9ucyk7DQogICAgICAgIC8vIOehruS/nei/lOWbnueahOaYr+aVsOe7hOagvOW8jw0KICAgICAgICByZXR1cm4gQXJyYXkuaXNBcnJheShwYXJzZWQpID8gcGFyc2VkIDogW107DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIC8vIOWmguaenOino+aekOWksei0pe+8jOWwneivleaMiemAl+WPt+WIhumalOeahOagvOW8j+WkhOeQhg0KICAgICAgICBpZiAodHlwZW9mIG9wdGlvbnMgPT09ICdzdHJpbmcnICYmIG9wdGlvbnMudHJpbSgpKSB7DQogICAgICAgICAgcmV0dXJuIG9wdGlvbnMuc3BsaXQoJywnKS5tYXAob3B0ID0+ICh7DQogICAgICAgICAgICB0ZXh0OiBvcHQudHJpbSgpLA0KICAgICAgICAgICAgZGlzYWJsZWQ6IGZhbHNlDQogICAgICAgICAgfSkpLmZpbHRlcihvcHQgPT4gb3B0LnRleHQpOw0KICAgICAgICB9DQogICAgICAgIHJldHVybiBbXTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmt7vliqDmu5rliqjljZXpgInpgInpobkgKi8NCiAgICBhZGRQaWNrZXJPcHRpb24oZmllbGQpIHsNCiAgICAgIGNvbnN0IGZpZWxkS2V5ID0gZmllbGQubmFtZSB8fCAndGVtcF8nICsgRGF0ZS5ub3coKTsNCiAgICAgIGlmICghdGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV0pIHsNCiAgICAgICAgdGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV0gPSB0aGlzLmdldFBpY2tlck9wdGlvbnMoZmllbGQub3B0aW9ucyk7DQogICAgICB9DQogICAgICB0aGlzLnBpY2tlckVkaXREYXRhW2ZpZWxkS2V5XS5wdXNoKHsNCiAgICAgICAgdGV4dDogJycsDQogICAgICAgIGRpc2FibGVkOiBmYWxzZQ0KICAgICAgfSk7DQogICAgICB0aGlzLnVwZGF0ZVBpY2tlck9wdGlvbnNGcm9tRWRpdChmaWVsZCk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5rua5Yqo5Y2V6YCJ6YCJ6aG5ICovDQogICAgcmVtb3ZlUGlja2VyT3B0aW9uKGZpZWxkLCBpbmRleCkgew0KICAgICAgY29uc3QgZmllbGRLZXkgPSBmaWVsZC5uYW1lIHx8ICd0ZW1wXycgKyBEYXRlLm5vdygpOw0KICAgICAgaWYgKHRoaXMucGlja2VyRWRpdERhdGFbZmllbGRLZXldKSB7DQogICAgICAgIHRoaXMucGlja2VyRWRpdERhdGFbZmllbGRLZXldLnNwbGljZShpbmRleCwgMSk7DQogICAgICAgIHRoaXMudXBkYXRlUGlja2VyT3B0aW9uc0Zyb21FZGl0KGZpZWxkKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDojrflj5bnlKjkuo7nvJbovpHnmoTmu5rliqjljZXpgInpgInpobkgKi8NCiAgICBnZXRQaWNrZXJPcHRpb25zRm9yRWRpdChmaWVsZCkgew0KICAgICAgY29uc3QgZmllbGRLZXkgPSBmaWVsZC5uYW1lIHx8ICd0ZW1wXycgKyBEYXRlLm5vdygpOw0KICAgICAgaWYgKCF0aGlzLnBpY2tlckVkaXREYXRhW2ZpZWxkS2V5XSkgew0KICAgICAgICAvLyDliJ3lp4vljJbnvJbovpHmlbDmja4NCiAgICAgICAgdGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV0gPSB0aGlzLmdldFBpY2tlck9wdGlvbnMoZmllbGQub3B0aW9ucyk7DQogICAgICB9DQogICAgICByZXR1cm4gdGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV07DQogICAgfSwNCiAgICAvKiog5LuO57yW6L6R5pWw5o2u5pu05paw5rua5Yqo5Y2V6YCJ6YCJ6aG5ICovDQogICAgdXBkYXRlUGlja2VyT3B0aW9uc0Zyb21FZGl0KGZpZWxkKSB7DQogICAgICBjb25zdCBmaWVsZEtleSA9IGZpZWxkLm5hbWUgfHwgJ3RlbXBfJyArIERhdGUubm93KCk7DQogICAgICBpZiAodGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV0pIHsNCiAgICAgICAgZmllbGQub3B0aW9ucyA9IEpTT04uc3RyaW5naWZ5KHRoaXMucGlja2VyRWRpdERhdGFbZmllbGRLZXldKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmm7TmlrDmu5rliqjljZXpgInpgInpobkgKi8NCiAgICB1cGRhdGVQaWNrZXJPcHRpb25zKGZpZWxkKSB7DQogICAgICAvLyDlu7bov5/mm7TmlrDvvIznoa7kv53mlbDmja7lt7Lnu4/lj5jmm7QNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgbGV0IHBpY2tlck9wdGlvbnMgPSB0aGlzLmdldFBpY2tlck9wdGlvbnMoZmllbGQub3B0aW9ucyk7DQogICAgICAgIGZpZWxkLm9wdGlvbnMgPSBKU09OLnN0cmluZ2lmeShwaWNrZXJPcHRpb25zKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWkhOeQhuWtl+auteexu+Wei+WPmOabtCAqLw0KICAgIGhhbmRsZUZpZWxkVHlwZUNoYW5nZShmaWVsZCkgew0KICAgICAgY29uc3QgZmllbGRLZXkgPSBmaWVsZC5uYW1lIHx8ICd0ZW1wXycgKyBEYXRlLm5vdygpOw0KDQogICAgICAvLyDlvZPliIfmjaLliLDmu5rliqjljZXpgInml7bvvIzlpoLmnpzmsqHmnInpgInpobnliJnliJ3lp4vljJYNCiAgICAgIGlmIChmaWVsZC50eXBlID09PSAncGlja2VyJykgew0KICAgICAgICBpZiAoIWZpZWxkLm9wdGlvbnMgfHwgZmllbGQub3B0aW9ucyA9PT0gJycpIHsNCiAgICAgICAgICBjb25zdCBkZWZhdWx0T3B0aW9ucyA9IFsNCiAgICAgICAgICAgIHsgdGV4dDogJ+mAiemhuTEnLCBkaXNhYmxlZDogZmFsc2UgfSwNCiAgICAgICAgICAgIHsgdGV4dDogJ+mAiemhuTInLCBkaXNhYmxlZDogZmFsc2UgfQ0KICAgICAgICAgIF07DQogICAgICAgICAgZmllbGQub3B0aW9ucyA9IEpTT04uc3RyaW5naWZ5KGRlZmF1bHRPcHRpb25zKTsNCiAgICAgICAgICB0aGlzLnBpY2tlckVkaXREYXRhW2ZpZWxkS2V5XSA9IGRlZmF1bHRPcHRpb25zOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOmHjeaWsOWIneWni+WMlue8lui+keaVsOaNrg0KICAgICAgICAgIHRoaXMucGlja2VyRWRpdERhdGFbZmllbGRLZXldID0gdGhpcy5nZXRQaWNrZXJPcHRpb25zKGZpZWxkLm9wdGlvbnMpOw0KICAgICAgICB9DQogICAgICB9DQogICAgICAvLyDlvZPliIfmjaLliLDlhbbku5bpnIDopoHpgInpobnnmoTlrZfmrrXnsbvlnovml7bvvIzlpoLmnpzmmK9KU09O5qC85byP5YiZ6L2s5o2i5Li66YCX5Y+35YiG6ZqUDQogICAgICBlbHNlIGlmICh0aGlzLm5lZWRPcHRpb25zKGZpZWxkLnR5cGUpICYmIGZpZWxkLnR5cGUgIT09ICdwaWNrZXInICYmIGZpZWxkLm9wdGlvbnMpIHsNCiAgICAgICAgLy8g5riF6Zmk57yW6L6R5pWw5o2u57yT5a2YDQogICAgICAgIGRlbGV0ZSB0aGlzLnBpY2tlckVkaXREYXRhW2ZpZWxkS2V5XTsNCg0KICAgICAgICB0cnkgew0KICAgICAgICAgIGNvbnN0IHBhcnNlZCA9IEpTT04ucGFyc2UoZmllbGQub3B0aW9ucyk7DQogICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkocGFyc2VkKSkgew0KICAgICAgICAgICAgZmllbGQub3B0aW9ucyA9IHBhcnNlZC5tYXAob3B0ID0+IG9wdC50ZXh0IHx8IG9wdC5sYWJlbCB8fCBvcHQpLmpvaW4oJywnKTsNCiAgICAgICAgICB9DQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICAvLyDlpoLmnpzkuI3mmK9KU09O5qC85byP77yM5L+d5oyB5Y6f5qC3DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOa4hemZpOe8lui+keaVsOaNrue8k+WtmA0KICAgICAgICBkZWxldGUgdGhpcy5waWNrZXJFZGl0RGF0YVtmaWVsZEtleV07DQogICAgICB9DQogICAgfSwNCiAgICAvKiog6I635Y+W6YCJ6aG56L6T5YWl5qGG5Y2g5L2N56ymICovDQogICAgZ2V0T3B0aW9uc1BsYWNlaG9sZGVyKHR5cGUpIHsNCiAgICAgIGNvbnN0IHBsYWNlaG9sZGVycyA9IHsNCiAgICAgICAgcmFkaW86ICfpgInpobnlhoXlrrnvvIzlpJrkuKrpgInpobnnlKjpgJflj7fliIbpmpTvvIzlpoLvvJrpgInpobkxLOmAiemhuTIs6YCJ6aG5MycsDQogICAgICAgIGNoZWNrYm94OiAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqU77yM5aaC77ya6YCJ6aG5MSzpgInpobkyLOmAiemhuTMnLA0KICAgICAgICBzZWxlY3Q6ICfpgInpobnlhoXlrrnvvIzlpJrkuKrpgInpobnnlKjpgJflj7fliIbpmpTvvIzlpoLvvJrpgInpobkxLOmAiemhuTIs6YCJ6aG5MycsDQogICAgICAgIHJhZGlvX290aGVyOiAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqU77yM5aaC77ya6YCJ6aG5MSzpgInpobkyLOmAiemhuTPvvIjkvJroh6rliqjmt7vliqAi5YW25LuWIumAiemhue+8iScsDQogICAgICAgIGNoZWNrYm94X290aGVyOiAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqU77yM5aaC77ya6YCJ6aG5MSzpgInpobkyLOmAiemhuTPvvIjkvJroh6rliqjmt7vliqAi5YW25LuWIumAiemhue+8iScsDQogICAgICAgIHNlbGVjdF9vdGhlcjogJ+mAiemhueWGheWuue+8jOWkmuS4qumAiemhueeUqOmAl+WPt+WIhumalO+8jOWmgu+8mumAiemhuTEs6YCJ6aG5MizpgInpobkz77yI5Lya6Ieq5Yqo5re75YqgIuWFtuS7liLpgInpobnvvIknLA0KICAgICAgICBwaWNrZXI6ICfmu5rliqjljZXpgInpgInpobnvvIzmlK/mjIHnpoHnlKjlip/og73vvIzor7fkvb/nlKjkuIrmlrnnmoTpgInpobnphY3nva7lmajov5vooYzorr7nva4nDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHBsYWNlaG9sZGVyc1t0eXBlXSB8fCAn6YCJ6aG55YaF5a6577yM5aSa5Liq6YCJ6aG555So6YCX5Y+35YiG6ZqUJzsNCiAgICB9LA0KICAgIC8qKiDojrflj5blrZfmrrXlm77moIcgKi8NCiAgICBnZXRGaWVsZEljb24odHlwZSkgew0KICAgICAgY29uc3QgaWNvbnMgPSB7DQogICAgICAgIGlucHV0OiAnZWwtaWNvbi1lZGl0JywNCiAgICAgICAgdGV4dGFyZWE6ICdlbC1pY29uLWRvY3VtZW50JywNCiAgICAgICAgbnVtYmVyOiAnZWwtaWNvbi1zLWRhdGEnLA0KICAgICAgICBlbWFpbDogJ2VsLWljb24tbWVzc2FnZScsDQogICAgICAgIHRlbDogJ2VsLWljb24tcGhvbmUnLA0KICAgICAgICByYWRpbzogJ2VsLWljb24tY2lyY2xlLWNoZWNrJywNCiAgICAgICAgY2hlY2tib3g6ICdlbC1pY29uLWNoZWNrJywNCiAgICAgICAgc2VsZWN0OiAnZWwtaWNvbi1hcnJvdy1kb3duJywNCiAgICAgICAgcmFkaW9fb3RoZXI6ICdlbC1pY29uLWNpcmNsZS1wbHVzLW91dGxpbmUnLA0KICAgICAgICBjaGVja2JveF9vdGhlcjogJ2VsLWljb24tY2lyY2xlLXBsdXMtb3V0bGluZScsDQogICAgICAgIHNlbGVjdF9vdGhlcjogJ2VsLWljb24tY2lyY2xlLXBsdXMtb3V0bGluZScsDQogICAgICAgIHBpY2tlcjogJ2VsLWljb24tc29ydCcsDQogICAgICAgIGRhdGU6ICdlbC1pY29uLWRhdGUnLA0KICAgICAgICBmaWxlOiAnZWwtaWNvbi11cGxvYWQnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIGljb25zW3R5cGVdIHx8ICdlbC1pY29uLWVkaXQnOw0KICAgIH0sDQogICAgLyoqIOiOt+WPluWtl+auteexu+Wei+WQjeensCAqLw0KICAgIGdldEZpZWxkVHlwZU5hbWUodHlwZSkgew0KICAgICAgY29uc3QgbmFtZXMgPSB7DQogICAgICAgIGlucHV0OiAn5paH5pys6L6T5YWlJywNCiAgICAgICAgdGV4dGFyZWE6ICflpJrooYzmlofmnKwnLA0KICAgICAgICBudW1iZXI6ICfmlbDlrZfovpPlhaUnLA0KICAgICAgICBlbWFpbDogJ+mCrueusScsDQogICAgICAgIHRlbDogJ+eUteivnScsDQogICAgICAgIHJhZGlvOiAn5Y2V6YCJJywNCiAgICAgICAgY2hlY2tib3g6ICflpJrpgIknLA0KICAgICAgICBzZWxlY3Q6ICfkuIvmi4npgInmi6knLA0KICAgICAgICByYWRpb19vdGhlcjogJ+WNlemAiSvlhbbku5YnLA0KICAgICAgICBjaGVja2JveF9vdGhlcjogJ+WkmumAiSvlhbbku5YnLA0KICAgICAgICBzZWxlY3Rfb3RoZXI6ICfkuIvmi4kr5YW25LuWJywNCiAgICAgICAgcGlja2VyOiAn5rua5Yqo5Y2V6YCJJywNCiAgICAgICAgZGF0ZTogJ+aXpeacnycsDQogICAgICAgIGZpbGU6ICfmlofku7bkuIrkvKAnDQogICAgICB9Ow0KICAgICAgcmV0dXJuIG5hbWVzW3R5cGVdIHx8ICfmnKrnn6XnsbvlnosnOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ21pbmlhcHAvaGFpdGFuZy9mb3JtQ29uZmlnL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYGZvcm1Db25maWdfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICAvKiog6LWe5Yqp5ZWG5Zu+54mH5LiK5Lyg5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlU3BvbnNvclVwbG9hZCgpIHsNCiAgICAgIHRoaXMubG9hZFNwb25zb3JJbWFnZSgpOw0KICAgICAgdGhpcy5zcG9uc29yRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICAvKiog5Yqg6L296LWe5Yqp5ZWG5Zu+54mHICovDQogICAgbG9hZFNwb25zb3JJbWFnZSgpIHsNCiAgICAgIGdldFNwb25zb3JJbWFnZSgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLnNwb25zb3JVbml0KSB7DQogICAgICAgICAgdGhpcy5jdXJyZW50U3BvbnNvckltYWdlID0gcmVzcG9uc2UuZGF0YS5zcG9uc29yVW5pdDsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRTcG9uc29ySW1hZ2UgPSAnJzsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICB0aGlzLmN1cnJlbnRTcG9uc29ySW1hZ2UgPSAnJzsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOS4iuS8oOWJjeagoemqjCAqLw0KICAgIGJlZm9yZVNwb25zb3JVcGxvYWQoZmlsZSkgew0KICAgICAgY29uc3QgaXNJbWFnZSA9IGZpbGUudHlwZS5pbmRleE9mKCdpbWFnZS8nKSA9PT0gMDsNCiAgICAgIGNvbnN0IGlzTHQ1TSA9IGZpbGUuc2l6ZSAvIDEwMjQgLyAxMDI0IDwgNTsNCg0KICAgICAgaWYgKCFpc0ltYWdlKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfkuIrkvKDmlofku7blj6rog73mmK/lm77niYfmoLzlvI8hJyk7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCiAgICAgIGlmICghaXNMdDVNKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfkuIrkvKDlm77niYflpKflsI/kuI3og73otoXov4cgNU1CIScpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgICByZXR1cm4gdHJ1ZTsNCiAgICB9LA0KICAgIC8qKiDkuIrkvKDmiJDlip/lm57osIMgKi8NCiAgICBoYW5kbGVTcG9uc29yVXBsb2FkU3VjY2VzcyhyZXNwb25zZSkgew0KICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICBjb25zdCBpbWFnZVVybCA9IHJlc3BvbnNlLnVybDsNCiAgICAgICAgdXBkYXRlU3BvbnNvckltYWdlKGltYWdlVXJsKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRTcG9uc29ySW1hZ2UgPSBpbWFnZVVybDsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfotZ7liqnllYblm77niYfkuIrkvKDmiJDlip8nKTsNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfkv53lrZjotZ7liqnllYblm77niYflpLHotKUnKTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcign5Zu+54mH5LiK5Lyg5aSx6LSl77yaJyArIHJlc3BvbnNlLm1zZyk7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5LiK5Lyg5aSx6LSl5Zue6LCDICovDQogICAgaGFuZGxlU3BvbnNvclVwbG9hZEVycm9yKCkgew0KICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+WbvueJh+S4iuS8oOWksei0pe+8jOivt+mHjeivlScpOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOi1nuWKqeWVhuWbvueJhyAqLw0KICAgIGhhbmRsZURlbGV0ZVNwb25zb3IoKSB7DQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfnoa7orqTliKDpmaTlvZPliY3otZ7liqnllYblm77niYfvvJ8nKS50aGVuKCgpID0+IHsNCiAgICAgICAgdXBkYXRlU3BvbnNvckltYWdlKCcnKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmN1cnJlbnRTcG9uc29ySW1hZ2UgPSAnJzsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfotZ7liqnllYblm77niYfliKDpmaTmiJDlip8nKTsNCiAgICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfliKDpmaTotZ7liqnllYblm77niYflpLHotKUnKTsNCiAgICAgICAgfSk7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfQ0KICB9DQp9Ow0K"}, null]}