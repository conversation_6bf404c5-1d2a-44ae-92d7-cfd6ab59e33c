
85fea335ca91da75ca0a543882422fb9aeff282a	{"key":"{\"terser\":\"4.8.1\",\"terser-webpack-plugin\":\"2.3.8\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.m?js(\\\\?.*)?$\", \"i\"),\"chunkFilter\":() => true,\"warningsFilter\":() => true,\"extractComments\":false,\"sourceMap\":false,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":true,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"compress\":{\"arrows\":false,\"collapse_vars\":false,\"comparisons\":false,\"computed_props\":false,\"hoist_funs\":false,\"hoist_props\":false,\"hoist_vars\":false,\"inline\":false,\"loops\":false,\"negate_iife\":false,\"properties\":false,\"reduce_funcs\":false,\"reduce_vars\":false,\"switches\":false,\"toplevel\":false,\"typeofs\":false,\"booleans\":true,\"if_return\":true,\"sequences\":true,\"unused\":true,\"conditionals\":true,\"dead_code\":true,\"evaluate\":true},\"mangle\":{\"safari10\":true}}},\"nodeVersion\":\"v22.15.1\",\"filename\":\"static\\u002Fjs\\u002Fruntime.8a8f031e.js\",\"contentHash\":\"10ac36440c3e420b3325\"}","integrity":"sha512-mH0CV97NHaW0w3aMBpslZCtxWOCMQCuFe/SB0FS0JvPdcvsZMQALwH7XGP2SIt4Qbp0xhRliXM9YxyhcPE3nqA==","time":1754385090206,"size":10277}