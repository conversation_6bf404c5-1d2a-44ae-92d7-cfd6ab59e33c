{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\haitang\\formConfig.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\api\\miniapp\\haitang\\formConfig.js", "mtime": 1754384002000}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\babel.config.js", "mtime": 1751427652172}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9yb290L0Rlc2t0b3AvXHU5ODc5XHU3NkVFXHU4QkIwXHU1RjU1XHVGRjA4XHU1NDM0XHU5Rjk5XHU5Rjk5XHVGRjA5L3RqdWhhaXRhbmdfbWluaWFwcC9ydW95aS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkRm9ybUNvbmZpZyA9IGFkZEZvcm1Db25maWc7CmV4cG9ydHMuZGVsRm9ybUNvbmZpZyA9IGRlbEZvcm1Db25maWc7CmV4cG9ydHMuZW5hYmxlRm9ybUNvbmZpZyA9IGVuYWJsZUZvcm1Db25maWc7CmV4cG9ydHMuZXhwb3J0Rm9ybUNvbmZpZyA9IGV4cG9ydEZvcm1Db25maWc7CmV4cG9ydHMuZ2V0RW5hYmxlZEZvcm1Db25maWcgPSBnZXRFbmFibGVkRm9ybUNvbmZpZzsKZXhwb3J0cy5nZXRGb3JtQ29uZmlnID0gZ2V0Rm9ybUNvbmZpZzsKZXhwb3J0cy5nZXRTcG9uc29ySW1hZ2UgPSBnZXRTcG9uc29ySW1hZ2U7CmV4cG9ydHMubGlzdEZvcm1Db25maWcgPSBsaXN0Rm9ybUNvbmZpZzsKZXhwb3J0cy51cGRhdGVGb3JtQ29uZmlnID0gdXBkYXRlRm9ybUNvbmZpZzsKZXhwb3J0cy51cGRhdGVTcG9uc29ySW1hZ2UgPSB1cGRhdGVTcG9uc29ySW1hZ2U7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6LlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva7liJfooagKZnVuY3Rpb24gbGlzdEZvcm1Db25maWcocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL2hhaXRhbmcvZm9ybUNvbmZpZy9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivouWkqeWkp+a1t+ajoOadr+mhueebruaKpeWQjeihqOWNlemFjee9ruivpue7hgpmdW5jdGlvbiBnZXRGb3JtQ29uZmlnKGNvbmZpZ0lkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvbWluaWFwcC9oYWl0YW5nL2Zvcm1Db25maWcvJyArIGNvbmZpZ0lkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7lpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva4KZnVuY3Rpb24gYWRkRm9ybUNvbmZpZyhkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvbWluaWFwcC9oYWl0YW5nL2Zvcm1Db25maWcnLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUueWkqeWkp+a1t+ajoOadr+mhueebruaKpeWQjeihqOWNlemFjee9rgpmdW5jdGlvbiB1cGRhdGVGb3JtQ29uZmlnKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL2hhaXRhbmcvZm9ybUNvbmZpZycsCiAgICBtZXRob2Q6ICdwdXQnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDliKDpmaTlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3ooajljZXphY3nva4KZnVuY3Rpb24gZGVsRm9ybUNvbmZpZyhjb25maWdJZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL21pbmlhcHAvaGFpdGFuZy9mb3JtQ29uZmlnLycgKyBjb25maWdJZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLy8g5ZCv55So6KGo5Y2V6YWN572uCmZ1bmN0aW9uIGVuYWJsZUZvcm1Db25maWcoY29uZmlnSWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9taW5pYXBwL2hhaXRhbmcvZm9ybUNvbmZpZy9lbmFibGUvJyArIGNvbmZpZ0lkLAogICAgbWV0aG9kOiAncHV0JwogIH0pOwp9CgovLyDojrflj5blkK/nlKjnmoTooajljZXphY3nva4KZnVuY3Rpb24gZ2V0RW5hYmxlZEZvcm1Db25maWcoKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvbWluaWFwcC9oYWl0YW5nL2Zvcm1Db25maWcvZW5hYmxlZCcsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOWvvOWHuuWkqeWkp+a1t+ajoOadr+mhueebruaKpeWQjeihqOWNlemFjee9rgpmdW5jdGlvbiBleHBvcnRGb3JtQ29uZmlnKHF1ZXJ5KSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvbWluaWFwcC9oYWl0YW5nL2Zvcm1Db25maWcvZXhwb3J0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDojrflj5botZ7liqnllYblm77niYcKZnVuY3Rpb24gZ2V0U3BvbnNvckltYWdlKCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL21pbmlhcHAvaGFpdGFuZy9wcm9qZWN0L2NvbmZpZy9zcG9uc29yJywKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5pu05paw6LWe5Yqp5ZWG5Zu+54mHCmZ1bmN0aW9uIHVwZGF0ZVNwb25zb3JJbWFnZShpbWFnZVVybCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL21pbmlhcHAvaGFpdGFuZy9wcm9qZWN0L2NvbmZpZy9zcG9uc29yJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiB7CiAgICAgIHNwb25zb3JVbml0OiBpbWFnZVVybAogICAgfQogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listFormConfig", "query", "request", "url", "method", "params", "getFormConfig", "configId", "addFormConfig", "data", "updateFormConfig", "delFormConfig", "enableFormConfig", "getEnabledFormConfig", "exportFormConfig", "getSponsorImage", "updateSponsorImage", "imageUrl", "sponsorUnit"], "sources": ["C:/Users/<USER>/Desktop/项目记录（吴龙龙）/tjuhaitang_miniapp/ruoyi-ui/src/api/miniapp/haitang/formConfig.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询天大海棠杯项目报名表单配置列表\r\nexport function listFormConfig(query) {\r\n  return request({\r\n    url: '/miniapp/haitang/formConfig/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询天大海棠杯项目报名表单配置详细\r\nexport function getFormConfig(configId) {\r\n  return request({\r\n    url: '/miniapp/haitang/formConfig/' + configId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增天大海棠杯项目报名表单配置\r\nexport function addFormConfig(data) {\r\n  return request({\r\n    url: '/miniapp/haitang/formConfig',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改天大海棠杯项目报名表单配置\r\nexport function updateFormConfig(data) {\r\n  return request({\r\n    url: '/miniapp/haitang/formConfig',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除天大海棠杯项目报名表单配置\r\nexport function delFormConfig(configId) {\r\n  return request({\r\n    url: '/miniapp/haitang/formConfig/' + configId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 启用表单配置\r\nexport function enableFormConfig(configId) {\r\n  return request({\r\n    url: '/miniapp/haitang/formConfig/enable/' + configId,\r\n    method: 'put'\r\n  })\r\n}\r\n\r\n// 获取启用的表单配置\r\nexport function getEnabledFormConfig() {\r\n  return request({\r\n    url: '/miniapp/haitang/formConfig/enabled',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 导出天大海棠杯项目报名表单配置\r\nexport function exportFormConfig(query) {\r\n  return request({\r\n    url: '/miniapp/haitang/formConfig/export',\r\n    method: 'post',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 获取赞助商图片\r\nexport function getSponsorImage() {\r\n  return request({\r\n    url: '/miniapp/haitang/project/config/sponsor',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 更新赞助商图片\r\nexport function updateSponsorImage(imageUrl) {\r\n  return request({\r\n    url: '/miniapp/haitang/project/config/sponsor',\r\n    method: 'put',\r\n    data: { sponsorUnit: imageUrl }\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACC,QAAQ,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B,GAAGI,QAAQ;IAC9CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAACD,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,aAAaA,CAACJ,QAAQ,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B,GAAGI,QAAQ;IAC9CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,gBAAgBA,CAACL,QAAQ,EAAE;EACzC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC,GAAGI,QAAQ;IACrDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,oBAAoBA,CAAA,EAAG;EACrC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,gBAAgBA,CAACb,KAAK,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,eAAeA,CAAA,EAAG;EAChC,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,kBAAkBA,CAACC,QAAQ,EAAE;EAC3C,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAE;MAAES,WAAW,EAAED;IAAS;EAChC,CAAC,CAAC;AACJ", "ignoreList": []}]}