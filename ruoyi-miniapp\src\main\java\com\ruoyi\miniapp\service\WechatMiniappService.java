package com.ruoyi.miniapp.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.miniapp.config.WechatMiniappConfig;
import com.ruoyi.miniapp.domain.dto.WechatJscode2sessionResponse;
import com.ruoyi.miniapp.utils.WechatDecryptUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序API服务
 * 
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
public class WechatMiniappService
{
    private static final Logger logger = LoggerFactory.getLogger(WechatMiniappService.class);

    @Autowired
    private WechatMiniappConfig wechatConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 通过code换取openid和session_key
     * 
     * @param code 微信登录凭证
     * @return 微信API响应
     */
    public WechatJscode2sessionResponse jscode2session(String code)
    {
        try
        {
            // 构建请求URL
            String url = buildJscode2sessionUrl(code);
            
            logger.info("调用微信jscode2session接口，URL: {}", url.replaceAll("secret=[^&]*", "secret=[HIDDEN]"));
            
            // 调用微信API
            String response = restTemplate.getForObject(url, String.class);
            
            logger.info("微信jscode2session接口响应: {}", response);
            
            // 解析响应
            WechatJscode2sessionResponse result = objectMapper.readValue(response, WechatJscode2sessionResponse.class);
            
            if (!result.isSuccess())
            {
                logger.error("微信jscode2session接口调用失败，errcode: {}, errmsg: {}", 
                           result.getErrcode(), result.getErrmsg());
                throw new RuntimeException("微信接口调用失败: " + result.getErrmsg());
            }
            
            return result;
        }
        catch (Exception e)
        {
            logger.error("调用微信jscode2session接口异常", e);
            throw new RuntimeException("获取微信用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 构建jscode2session请求URL
     */
    private String buildJscode2sessionUrl(String code)
    {
        StringBuilder url = new StringBuilder();
        url.append(wechatConfig.getFullJscode2sessionUrl());
        url.append("?appid=").append(wechatConfig.getAppid());
        url.append("&secret=").append(wechatConfig.getSecret());
        url.append("&js_code=").append(code);
        url.append("&grant_type=authorization_code");

        return url.toString();
    }

    /**
     * 构建getPhoneNumber请求URL
     */
    private String buildGetPhoneNumberUrl()
    {
        // 需要先获取access_token
        String accessToken = getAccessToken();
        return "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + accessToken;
    }

    /**
     * 获取微信access_token
     */
    private String getAccessToken()
    {
        try
        {
            String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="
                        + wechatConfig.getAppid() + "&secret=" + wechatConfig.getSecret();

            String response = restTemplate.getForObject(url, String.class);

            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> responseMap = objectMapper.readValue(response, Map.class);

            Integer errcode = (Integer) responseMap.get("errcode");
            if (errcode != null && errcode != 0)
            {
                String errmsg = (String) responseMap.get("errmsg");
                logger.error("获取access_token失败，errcode: {}, errmsg: {}", errcode, errmsg);
                throw new RuntimeException("获取access_token失败: " + errmsg);
            }

            String accessToken = (String) responseMap.get("access_token");
            if (accessToken == null)
            {
                throw new RuntimeException("获取access_token失败，返回数据格式错误");
            }

            logger.info("获取access_token成功");
            return accessToken;
        }
        catch (Exception e)
        {
            logger.error("获取access_token异常", e);
            throw new RuntimeException("获取access_token失败: " + e.getMessage());
        }
    }

    /**
     * 通过code获取手机号信息（新版本）
     *
     * @param phoneCode 手机号获取凭证
     * @return 手机号信息
     */
    public WechatDecryptUtils.WechatPhoneInfo getPhoneNumberByCode(String phoneCode)
    {
        try
        {
            logger.info("开始通过code获取手机号信息");

            // 构建请求URL
            String url = buildGetPhoneNumberUrl();

            // 构建请求体
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("code", phoneCode);

            logger.info("调用微信getPhoneNumber接口");

            // 调用微信API
            String response = restTemplate.postForObject(url, requestBody, String.class);

            logger.info("微信getPhoneNumber接口响应: {}", response);

            // 解析响应
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> responseMap = objectMapper.readValue(response, Map.class);

            Integer errcode = (Integer) responseMap.get("errcode");
            if (errcode != null && errcode != 0)
            {
                String errmsg = (String) responseMap.get("errmsg");
                logger.error("微信getPhoneNumber接口调用失败，errcode: {}, errmsg: {}", errcode, errmsg);
                throw new RuntimeException("微信接口调用失败: " + errmsg);
            }

            // 解析手机号信息
            Map<String, Object> phoneInfo = (Map<String, Object>) responseMap.get("phone_info");
            if (phoneInfo == null)
            {
                throw new RuntimeException("微信接口返回数据格式错误，未找到phone_info");
            }

            WechatDecryptUtils.WechatPhoneInfo result = new WechatDecryptUtils.WechatPhoneInfo();
            result.setPhoneNumber((String) phoneInfo.get("phoneNumber"));
            result.setPurePhoneNumber((String) phoneInfo.get("purePhoneNumber"));
            result.setCountryCode(String.valueOf(phoneInfo.get("countryCode")));

            logger.info("手机号获取成功: {}", result.getPurePhoneNumber());
            return result;
        }
        catch (Exception e)
        {
            logger.error("通过code获取手机号失败", e);
            throw new RuntimeException("获取手机号失败: " + e.getMessage());
        }
    }

    /**
     * 解密手机号信息（旧版本，保持兼容性）
     *
     * @param encryptedData 加密数据
     * @param sessionKey 会话密钥
     * @param iv 初始向量
     * @return 手机号信息
     */
    @Deprecated
    public WechatDecryptUtils.WechatPhoneInfo decryptPhoneNumber(String encryptedData, String sessionKey, String iv)
    {
        try
        {
            logger.info("开始解密手机号信息（旧版本）");
            WechatDecryptUtils.WechatPhoneInfo phoneInfo = WechatDecryptUtils.decryptPhoneNumber(encryptedData, sessionKey, iv);
            logger.info("手机号解密成功: {}", phoneInfo.getPurePhoneNumber());
            return phoneInfo;
        }
        catch (Exception e)
        {
            logger.error("解密手机号失败", e);
            throw new RuntimeException("解密手机号失败: " + e.getMessage());
        }
    }

    /**
     * 解密用户信息
     *
     * @param encryptedData 加密数据
     * @param sessionKey 会话密钥
     * @param iv 初始向量
     * @return 用户信息
     */
    public WechatDecryptUtils.WechatUserInfo decryptUserInfo(String encryptedData, String sessionKey, String iv)
    {
        try
        {
            logger.info("开始解密用户信息");
            WechatDecryptUtils.WechatUserInfo userInfo = WechatDecryptUtils.decryptUserInfo(encryptedData, sessionKey, iv);
            logger.info("用户信息解密成功: {}", userInfo.getNickName());
            return userInfo;
        }
        catch (Exception e)
        {
            logger.error("解密用户信息失败", e);
            throw new RuntimeException("解密用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取小程序码
     *
     * @param path 扫码进入的小程序页面路径，最大长度 1024 个字符，不能为空
     * @param width 二维码的宽度，单位 px。默认值为430，最小 280px，最大 1280px
     * @param autoColor 自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调
     * @param lineColor auto_color 为 false 时生效，使用 rgb 设置颜色
     * @param isHyaline 是否需要透明底色，为 true 时，生成透明底色的小程序码
     * @param envVersion 要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
     * @return Base64编码的图片数据
     */
    public String getQRCode(String path, Integer width, Boolean autoColor, Map<String, Integer> lineColor, Boolean isHyaline, String envVersion)
    {
        try
        {
            logger.info("开始生成小程序码，path: {}", path);

            // 获取access_token
            String accessToken = getAccessToken();

            // 构建请求URL
            String url = "https://api.weixin.qq.com/wxa/getwxacode?access_token=" + accessToken;

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("path", path);

            // 设置可选参数
            if (width != null) {
                requestBody.put("width", width);
            } else {
                requestBody.put("width", 430); // 默认宽度
            }

            if (autoColor != null) {
                requestBody.put("auto_color", autoColor);
            } else {
                requestBody.put("auto_color", false); // 默认不自动配色
            }

            if (lineColor != null && !lineColor.isEmpty()) {
                requestBody.put("line_color", lineColor);
            } else {
                // 默认黑色
                Map<String, Integer> defaultColor = new HashMap<>();
                defaultColor.put("r", 0);
                defaultColor.put("g", 0);
                defaultColor.put("b", 0);
                requestBody.put("line_color", defaultColor);
            }

            if (isHyaline != null) {
                requestBody.put("is_hyaline", isHyaline);
            } else {
                requestBody.put("is_hyaline", false); // 默认不透明
            }

            if (envVersion != null && !envVersion.isEmpty()) {
                requestBody.put("env_version", envVersion);
            } else {
//                requestBody.put("env_version", "release"); // 默认正式版
                requestBody.put("env_version", "trial"); // 体验版
//                requestBody.put("env_version", "develop"); // 开发版
            }

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            logger.info("调用微信小程序码生成接口，URL: {}", url);

            // 发起请求
            ResponseEntity<byte[]> response = restTemplate.exchange(url, HttpMethod.POST, request, byte[].class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null)
            {
                byte[] imageBytes = response.getBody();

                // 检查返回的是否是图片数据还是错误JSON
                String responseStr = new String(imageBytes);
                if (responseStr.startsWith("{") && responseStr.contains("errcode"))
                {
                    // 返回的是错误JSON
                    logger.error("微信小程序码生成失败，响应: {}", responseStr);

                    Map<String, Object> errorMap = objectMapper.readValue(responseStr, Map.class);
                    Integer errcode = (Integer) errorMap.get("errcode");
                    String errmsg = (String) errorMap.get("errmsg");

                    throw new RuntimeException("微信小程序码生成失败: " + errcode + " - " + errmsg);
                }

                // 返回的是图片数据，转换为Base64
                String base64Image = Base64.getEncoder().encodeToString(imageBytes);
                logger.info("小程序码生成成功，图片大小: {} bytes", imageBytes.length);

                return base64Image;
            }
            else
            {
                throw new RuntimeException("微信小程序码生成失败，HTTP状态码: " + response.getStatusCode());
            }
        }
        catch (Exception e)
        {
            logger.error("生成小程序码异常", e);
            throw new RuntimeException("生成小程序码失败: " + e.getMessage());
        }
    }

    /**
     * 获取活动详情页面的小程序码
     *
     * @param eventId 活动ID
     * @return Base64编码的图片数据
     */
    public String getEventQRCode(Long eventId)
    {
        // 构建活动详情页面路径
        String path = "pages/event/detail?eventId=" + eventId;

        // 使用默认参数生成小程序码
        return getQRCode(path, 430, false, null, false, "release");
    }

    /**
     * 验证配置是否完整
     */
    public boolean isConfigValid()
    {
        return wechatConfig.getAppid() != null && !wechatConfig.getAppid().isEmpty() &&
               wechatConfig.getSecret() != null && !wechatConfig.getSecret().isEmpty() &&
               !wechatConfig.getAppid().equals("your_miniapp_appid") &&
               !wechatConfig.getSecret().equals("your_miniapp_secret");
    }
}
