#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试项目报名API的脚本
"""

import requests
import json

# API基础URL（需要根据实际情况修改）
BASE_URL = "http://localhost:8086"

def test_registration():
    """测试项目报名接口"""
    url = f"{BASE_URL}/miniapp/haitang/project/app/register"
    
    # 读取测试数据
    with open('test_registration.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        print(f"注册接口测试:")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"注册接口测试失败: {e}")
        return False

def test_user_projects(user_id):
    """测试根据用户ID查询项目接口"""
    url = f"{BASE_URL}/miniapp/haitang/project/app/user/{user_id}"
    
    try:
        response = requests.get(url)
        print(f"\n用户项目查询接口测试:")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"用户项目查询接口测试失败: {e}")
        return False

def test_project_list():
    """测试项目列表接口"""
    url = f"{BASE_URL}/miniapp/haitang/project/list"
    
    try:
        response = requests.get(url)
        print(f"\n项目列表接口测试:")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"项目列表接口测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始API测试...")
    
    # 测试注册接口
    registration_success = test_registration()
    
    # 测试用户项目查询接口
    user_projects_success = test_user_projects(12345)
    
    # 测试项目列表接口
    list_success = test_project_list()
    
    print(f"\n测试结果:")
    print(f"注册接口: {'✓' if registration_success else '✗'}")
    print(f"用户项目查询: {'✓' if user_projects_success else '✗'}")
    print(f"项目列表: {'✓' if list_success else '✗'}")
