{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectRegistration\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\src\\views\\miniapp\\haitang\\projectRegistration\\index.vue", "mtime": 1754384929304}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751437915130}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751437913990}, {"path": "C:\\Users\\<USER>\\Desktop\\项目记录（吴龙龙）\\tjuhaitang_miniapp\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751437915666}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UHJvamVjdFJlZ2lzdHJhdGlvbiwgZ2V0UHJvamVjdFJlZ2lzdHJhdGlvbiwgZGVsUHJvamVjdFJlZ2lzdHJhdGlvbiB9IGZyb20gIkAvYXBpL21pbmlhcHAvaGFpdGFuZy9wcm9qZWN0UmVnaXN0cmF0aW9uIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiUHJvamVjdFJlZ2lzdHJhdGlvbiIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3orrDlvZXooajmoLzmlbDmja4NCiAgICAgIHJlZ2lzdHJhdGlvbkxpc3Q6IFtdLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHVzZXJOYW1lOiBudWxsLA0KICAgICAgICB1c2VyUGhvbmU6IG51bGwNCiAgICAgIH0sDQogICAgICAvLyDmn6XnnIvooajljZXlj4LmlbANCiAgICAgIHZpZXdGb3JtOiB7fSwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuafpeeci+W8ueWHuuWxgg0KICAgICAgdmlld09wZW46IGZhbHNlDQogICAgfTsNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvKiog6Kej5p6Q5ZCO55qE6KGo5Y2V5pWw5o2uICovDQogICAgcGFyc2VkRm9ybURhdGEoKSB7DQogICAgICBpZiAoIXRoaXMudmlld0Zvcm0uZm9ybURhdGEpIHJldHVybiBbXTsNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgZm9ybURhdGEgPSBKU09OLnBhcnNlKHRoaXMudmlld0Zvcm0uZm9ybURhdGEpOw0KDQogICAgICAgIC8vIOajgOafpeaVsOaNruagvOW8j++8muWmguaenOaYr+aVsOe7hOagvOW8j++8iOaWsOagvOW8j++8ie+8jOebtOaOpeWkhOeQhg0KICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShmb3JtRGF0YSkpIHsNCiAgICAgICAgICByZXR1cm4gZm9ybURhdGEubWFwKGZpZWxkID0+ICh7DQogICAgICAgICAgICBuYW1lOiBmaWVsZC5uYW1lLA0KICAgICAgICAgICAgbGFiZWw6IGZpZWxkLmxhYmVsIHx8IHRoaXMuZ2V0RGVmYXVsdEZpZWxkTGFiZWwoZmllbGQubmFtZSkgfHwgZmllbGQubmFtZSwNCiAgICAgICAgICAgIHZhbHVlOiB0aGlzLmZvcm1hdEZpZWxkVmFsdWUoZmllbGQudmFsdWUsIGZpZWxkLnR5cGUpLA0KICAgICAgICAgICAgdHlwZTogZmllbGQudHlwZSB8fCAnaW5wdXQnLA0KICAgICAgICAgICAgZmlsZXM6IGZpZWxkLnR5cGUgPT09ICdmaWxlJyA/IHRoaXMucGFyc2VGaWxlVmFsdWUoZmllbGQudmFsdWUpIDogbnVsbCwNCiAgICAgICAgICAgIHJlcXVpcmVkOiBmaWVsZC5yZXF1aXJlZCB8fCBmYWxzZSwNCiAgICAgICAgICAgIG9wdGlvbnM6IGZpZWxkLm9wdGlvbnMgfHwgJycNCiAgICAgICAgICB9KSk7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDlhbzlrrnml6fmoLzlvI/vvJrlr7nosaHmoLzlvI/nmoTmlbDmja4NCiAgICAgICAgY29uc3QgY29uZmlnRGF0YSA9IHRoaXMudmlld0Zvcm0uY29uZmlnRGF0YSA/IEpTT04ucGFyc2UodGhpcy52aWV3Rm9ybS5jb25maWdEYXRhKSA6IFtdOw0KDQogICAgICAgIC8vIOWIm+W7uuWtl+autemFjee9ruaYoOWwhA0KICAgICAgICBjb25zdCBmaWVsZENvbmZpZ01hcCA9IHt9Ow0KICAgICAgICBjb25maWdEYXRhLmZvckVhY2goZmllbGQgPT4gew0KICAgICAgICAgIGZpZWxkQ29uZmlnTWFwW2ZpZWxkLm5hbWVdID0gZmllbGQ7DQogICAgICAgIH0pOw0KDQogICAgICAgIC8vIOino+aekOihqOWNleaVsOaNrg0KICAgICAgICBjb25zdCBwYXJzZWREYXRhID0gW107DQogICAgICAgIGZvciAoY29uc3QgW2ZpZWxkTmFtZSwgZmllbGRWYWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoZm9ybURhdGEpKSB7DQogICAgICAgICAgY29uc3QgZmllbGRDb25maWcgPSBmaWVsZENvbmZpZ01hcFtmaWVsZE5hbWVdIHx8IHt9Ow0KDQogICAgICAgICAgLy8g5LyY5YWI5L2/55So6YWN572u5Lit55qE5Lit5paH5qCH562+77yM5aaC5p6c5rKh5pyJ5YiZ5L2/55So5a2X5q615ZCNDQogICAgICAgICAgY29uc3QgZGlzcGxheUxhYmVsID0gZmllbGRDb25maWcubGFiZWwgfHwgdGhpcy5nZXREZWZhdWx0RmllbGRMYWJlbChmaWVsZE5hbWUpIHx8IGZpZWxkTmFtZTsNCg0KICAgICAgICAgIHBhcnNlZERhdGEucHVzaCh7DQogICAgICAgICAgICBuYW1lOiBmaWVsZE5hbWUsDQogICAgICAgICAgICBsYWJlbDogZGlzcGxheUxhYmVsLA0KICAgICAgICAgICAgdmFsdWU6IHRoaXMuZm9ybWF0RmllbGRWYWx1ZShmaWVsZFZhbHVlLCBmaWVsZENvbmZpZy50eXBlKSwNCiAgICAgICAgICAgIHR5cGU6IGZpZWxkQ29uZmlnLnR5cGUgfHwgJ2lucHV0JywNCiAgICAgICAgICAgIGZpbGVzOiBmaWVsZENvbmZpZy50eXBlID09PSAnZmlsZScgPyB0aGlzLnBhcnNlRmlsZVZhbHVlKGZpZWxkVmFsdWUpIDogbnVsbA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQoNCiAgICAgICAgcmV0dXJuIHBhcnNlZERhdGE7DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+ino+aekOihqOWNleaVsOaNruWksei0pTonLCBlKTsNCiAgICAgICAgcmV0dXJuIFtdOw0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6LlpKnlpKfmtbfmo6Dmna/pobnnm67miqXlkI3orrDlvZXliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RQcm9qZWN0UmVnaXN0cmF0aW9uKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnJlZ2lzdHJhdGlvbkxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ucmVnaXN0cmF0aW9uSWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGghPT0xDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUoKSB7DQogICAgICAvLyDov5nph4zlj6/ku6Xmt7vliqDkv67mlLnpgLvovpHvvIzmmoLml7bkuI3lrp7njrANCiAgICAgIHRoaXMuJG1vZGFsLm1zZ0luZm8oIuS/ruaUueWKn+iDveaaguacquWunueOsCIpOw0KICAgIH0sDQogICAgLyoqIOafpeeci+aMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVZpZXcocm93KSB7DQogICAgICBnZXRQcm9qZWN0UmVnaXN0cmF0aW9uKHJvdy5yZWdpc3RyYXRpb25JZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMudmlld0Zvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLnZpZXdPcGVuID0gdHJ1ZTsNCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgcmVnaXN0cmF0aW9uSWRzID0gcm93LnJlZ2lzdHJhdGlvbklkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5aSp5aSn5rW35qOg5p2v6aG555uu5oql5ZCN6K6w5b2V57yW5Y+35Li6IicgKyByZWdpc3RyYXRpb25JZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxQcm9qZWN0UmVnaXN0cmF0aW9uKHJlZ2lzdHJhdGlvbklkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ21pbmlhcHAvaGFpdGFuZy9yZWdpc3RyYXRpb24vZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgcHJvamVjdFJlZ2lzdHJhdGlvbl8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9LA0KICAgIC8qKiDmoLzlvI/ljJblrZfmrrXlgLwgKi8NCiAgICBmb3JtYXRGaWVsZFZhbHVlKHZhbHVlLCBmaWVsZFR5cGUpIHsNCiAgICAgIC8vIOWkhOeQhuepuuWAvA0KICAgICAgaWYgKHZhbHVlID09PSBudWxsIHx8IHZhbHVlID09PSB1bmRlZmluZWQgfHwgdmFsdWUgPT09ICcnKSB7DQogICAgICAgIHJldHVybiAnJzsNCiAgICAgIH0NCg0KICAgICAgc3dpdGNoIChmaWVsZFR5cGUpIHsNCiAgICAgICAgY2FzZSAnY2hlY2tib3gnOg0KICAgICAgICBjYXNlICdjaGVja2JveF9vdGhlcic6DQogICAgICAgICAgLy8g5aSa6YCJ5YC86YCa5bi45piv5pWw57uEDQogICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7DQogICAgICAgICAgICByZXR1cm4gdmFsdWUuZmlsdGVyKHYgPT4gdiAhPT0gbnVsbCAmJiB2ICE9PSB1bmRlZmluZWQgJiYgdiAhPT0gJycpOw0KICAgICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJykgew0KICAgICAgICAgICAgLy8g5aaC5p6c5piv5a2X56ym5Liy77yM5bCd6K+V5oyJ6YCX5Y+35YiG5YmyDQogICAgICAgICAgICByZXR1cm4gdmFsdWUuc3BsaXQoJywnKS5tYXAodiA9PiB2LnRyaW0oKSkuZmlsdGVyKHYgPT4gdiAhPT0gJycpOw0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gW3ZhbHVlXTsNCiAgICAgICAgY2FzZSAncmFkaW8nOg0KICAgICAgICBjYXNlICdyYWRpb19vdGhlcic6DQogICAgICAgIGNhc2UgJ3NlbGVjdCc6DQogICAgICAgIGNhc2UgJ3NlbGVjdF9vdGhlcic6DQogICAgICAgIGNhc2UgJ3BpY2tlcic6DQogICAgICAgICAgLy8g5Y2V6YCJ5YC877yM5aSE55CG56m65a2X56ym5Liy55qE5oOF5Ya1DQogICAgICAgICAgcmV0dXJuIHZhbHVlICYmIHZhbHVlICE9PSAnJyA/IFN0cmluZyh2YWx1ZSkgOiAnJzsNCiAgICAgICAgY2FzZSAnZmlsZSc6DQogICAgICAgICAgLy8g5paH5Lu257G75Z6L54m55q6K5aSE55CGDQogICAgICAgICAgcmV0dXJuIHZhbHVlID8gJ+afpeeci+aWh+S7ticgOiAnJzsNCiAgICAgICAgY2FzZSAnZGF0ZSc6DQogICAgICAgICAgLy8g5pel5pyf5qC85byP5YyWDQogICAgICAgICAgaWYgKHZhbHVlICYmIHZhbHVlICE9PSAnJykgew0KICAgICAgICAgICAgLy8g5aaC5p6c5piv5qCH5YeG5pel5pyf5qC85byP77yM55u05o6l6L+U5ZueDQogICAgICAgICAgICBpZiAoL15cZHs0fS1cZHsyfS1cZHsyfSQvLnRlc3QodmFsdWUpKSB7DQogICAgICAgICAgICAgIHJldHVybiB2YWx1ZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIC8vIOWQpuWImeWwneivleagvOW8j+WMlg0KICAgICAgICAgICAgcmV0dXJuIHRoaXMucGFyc2VUaW1lID8gdGhpcy5wYXJzZVRpbWUodmFsdWUsICd7eX0te219LXtkfScpIDogdmFsdWU7DQogICAgICAgICAgfQ0KICAgICAgICAgIHJldHVybiAnJzsNCiAgICAgICAgY2FzZSAnZGF0ZXRpbWUnOg0KICAgICAgICAgIC8vIOaXpeacn+aXtumXtOagvOW8j+WMlg0KICAgICAgICAgIGlmICh2YWx1ZSAmJiB2YWx1ZSAhPT0gJycpIHsNCiAgICAgICAgICAgIHJldHVybiB0aGlzLnBhcnNlVGltZSA/IHRoaXMucGFyc2VUaW1lKHZhbHVlLCAne3l9LXttfS17ZH0ge2h9OntpfTp7c30nKSA6IHZhbHVlOw0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gJyc7DQogICAgICAgIGNhc2UgJ251bWJlcic6DQogICAgICAgICAgLy8g5pWw5a2X5qC85byP5YyWDQogICAgICAgICAgcmV0dXJuIHZhbHVlID8gU3RyaW5nKHZhbHVlKSA6ICcnOw0KICAgICAgICBjYXNlICd0ZXh0YXJlYSc6DQogICAgICAgICAgLy8g5aSa6KGM5paH5pysDQogICAgICAgICAgcmV0dXJuIFN0cmluZyh2YWx1ZSk7DQogICAgICAgIGNhc2UgJ2lucHV0JzoNCiAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICByZXR1cm4gU3RyaW5nKHZhbHVlKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDop6PmnpDmlofku7blgLwgKi8NCiAgICBwYXJzZUZpbGVWYWx1ZSh2YWx1ZSkgew0KICAgICAgaWYgKCF2YWx1ZSkgcmV0dXJuIFtdOw0KDQogICAgICB0cnkgew0KICAgICAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJykgew0KICAgICAgICAgIC8vIOajgOafpeaYr+WQpuaYr1VSTOagvOW8j++8iOS7pWh0dHDlvIDlpLTvvIkNCiAgICAgICAgICBpZiAodmFsdWUuc3RhcnRzV2l0aCgnaHR0cDovLycpIHx8IHZhbHVlLnN0YXJ0c1dpdGgoJ2h0dHBzOi8vJykpIHsNCiAgICAgICAgICAgIC8vIOS7jlVSTOS4reaPkOWPluaWh+S7tuWQjQ0KICAgICAgICAgICAgY29uc3QgZmlsZU5hbWUgPSB2YWx1ZS5zcGxpdCgnLycpLnBvcCgpIHx8ICfmlofku7YnOw0KICAgICAgICAgICAgcmV0dXJuIFt7IG5hbWU6IGZpbGVOYW1lLCB1cmw6IHZhbHVlIH1dOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOWwneivleino+aekEpTT07lrZfnrKbkuLINCiAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZSh2YWx1ZSk7DQogICAgICAgICAgICByZXR1cm4gQXJyYXkuaXNBcnJheShwYXJzZWQpID8gcGFyc2VkIDogW3BhcnNlZF07DQogICAgICAgICAgfSBjYXRjaCAoanNvbkVycm9yKSB7DQogICAgICAgICAgICAvLyDlpoLmnpzkuI3mmK9KU09O77yM5b2T5L2c5pmu6YCa5paH5Lu25ZCN5aSE55CGDQogICAgICAgICAgICByZXR1cm4gW3sgbmFtZTogdmFsdWUsIHVybDogdmFsdWUgfV07DQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2UgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7DQogICAgICAgICAgcmV0dXJuIHZhbHVlOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHJldHVybiBbdmFsdWVdOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIC8vIOWmguaenOino+aekOWksei0pe+8jOW9k+S9nOaZrumAmuWtl+espuS4suWkhOeQhg0KICAgICAgICByZXR1cm4gW3sgbmFtZTogdmFsdWUsIHVybDogdmFsdWUgfV07DQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDojrflj5bpu5jorqTlrZfmrrXmoIfnrb4gKi8NCiAgICBnZXREZWZhdWx0RmllbGRMYWJlbChmaWVsZE5hbWUpIHsNCiAgICAgIGNvbnN0IGxhYmVsTWFwID0gew0KICAgICAgICAvLyDpobnnm67nm7jlhbMNCiAgICAgICAgJ3Byb2plY3ROYW1lJzogJ+mhueebruWQjeensCcsDQogICAgICAgICdwcm9qZWN0RGVzYyc6ICfpobnnm67nroDku4snLA0KICAgICAgICAncHJvamVjdERlc2NyaXB0aW9uJzogJ+mhueebruaPj+i/sCcsDQogICAgICAgICdwcm9qZWN0VHlwZSc6ICfpobnnm67nsbvlnosnLA0KICAgICAgICAncHJvamVjdFN0YWdlJzogJ+mhueebrumYtuautScsDQogICAgICAgICdwcm9qZWN0Q2F0ZWdvcnknOiAn6aG555uu5YiG57G7JywNCg0KICAgICAgICAvLyDlm6LpmJ/nm7jlhbMNCiAgICAgICAgJ3RlYW1MZWFkZXInOiAn5Zui6Zif6LSf6LSj5Lq6JywNCiAgICAgICAgJ3RlYW1TaXplJzogJ+WboumYn+inhOaooScsDQogICAgICAgICd0ZWFtTWVtYmVyJzogJ+WboumYn+aIkOWRmCcsDQogICAgICAgICd0ZWFtRGVzY3JpcHRpb24nOiAn5Zui6Zif5LuL57uNJywNCg0KICAgICAgICAvLyDogZTns7vkv6Hmga8NCiAgICAgICAgJ25hbWUnOiAn5aeT5ZCNJywNCiAgICAgICAgJ3VzZXJOYW1lJzogJ+eUqOaIt+Wnk+WQjScsDQogICAgICAgICdwaG9uZSc6ICfogZTns7vnlLXor50nLA0KICAgICAgICAndXNlclBob25lJzogJ+eUqOaIt+aJi+acuuWPtycsDQogICAgICAgICdlbWFpbCc6ICfpgq7nrrHlnLDlnYAnLA0KICAgICAgICAndXNlckVtYWlsJzogJ+eUqOaIt+mCrueusScsDQogICAgICAgICdhZGRyZXNzJzogJ+iBlOezu+WcsOWdgCcsDQogICAgICAgICdjb21wYW55JzogJ+aJgOWcqOWFrOWPuCcsDQogICAgICAgICdwb3NpdGlvbic6ICfogYzkvY0nLA0KDQogICAgICAgIC8vIOi6q+S7veS/oeaBrw0KICAgICAgICAnaWRjYXJkJzogJ+i6q+S7veivgeWPtycsDQogICAgICAgICdzdHVkZW50SWQnOiAn5a2m5Y+3JywNCiAgICAgICAgJ3dvcmtJZCc6ICflt6Xlj7cnLA0KDQogICAgICAgIC8vIOaXtumXtOebuOWFsw0KICAgICAgICAncmVnaXN0cmF0aW9uRGF0ZSc6ICfmiqXlkI3ml6XmnJ8nLA0KICAgICAgICAncmVnaXN0cmF0aW9uVGltZSc6ICfmiqXlkI3ml7bpl7QnLA0KICAgICAgICAnc3RhcnREYXRlJzogJ+W8gOWni+aXpeacnycsDQogICAgICAgICdlbmREYXRlJzogJ+e7k+adn+aXpeacnycsDQogICAgICAgICdiaXJ0aERhdGUnOiAn5Ye655Sf5pel5pyfJywNCg0KICAgICAgICAvLyDmlofku7bnm7jlhbMNCiAgICAgICAgJ3BsYW5GaWxlJzogJ+mhueebruiuoeWIkuS5picsDQogICAgICAgICd2aWRlb0ZpbGUnOiAn5ryU56S66KeG6aKRJywNCiAgICAgICAgJ3Jlc3VtZUZpbGUnOiAn5Liq5Lq6566A5Y6GJywNCiAgICAgICAgJ2NlcnRpZmljYXRlRmlsZSc6ICfor4Hkuabmlofku7YnLA0KICAgICAgICAnYXR0YWNobWVudEZpbGUnOiAn6ZmE5Lu25paH5Lu2JywNCg0KICAgICAgICAvLyDpgInmi6nnm7jlhbMNCiAgICAgICAgJ2dlbmRlcic6ICfmgKfliKsnLA0KICAgICAgICAnZWR1Y2F0aW9uJzogJ+WtpuWOhicsDQogICAgICAgICdleHBlcmllbmNlJzogJ+W3peS9nOe7j+mqjCcsDQogICAgICAgICdza2lsbCc6ICfmioDog70nLA0KICAgICAgICAnaW50ZXJlc3QnOiAn5YW06Laj54ix5aW9JywNCiAgICAgICAgJ2hvYmJ5JzogJ+eIseWlvScsDQoNCiAgICAgICAgLy8g5Lia5Yqh55u45YWzDQogICAgICAgICdpbmR1c3RyeSc6ICfmiYDlsZ7ooYzkuJonLA0KICAgICAgICAnZnVuZGluZ1JvdW5kJzogJ+iejei1hOi9ruasoScsDQogICAgICAgICdpbnZlc3RtZW50JzogJ+aKlei1hOmHkeminScsDQogICAgICAgICdyZXZlbnVlJzogJ+iQpeaUtuaDheWGtScsDQogICAgICAgICd3ZWJzaXRlJzogJ+e9keermeWcsOWdgCcsDQogICAgICAgICdzb2NpYWxNZWRpYSc6ICfnpL7kuqTlqpLkvZMnLA0KDQogICAgICAgIC8vIOWFtuS7lg0KICAgICAgICAncmVtYXJrJzogJ+Wkh+azqCcsDQogICAgICAgICdjb21tZW50JzogJ+ivhOiuuicsDQogICAgICAgICdmZWVkYmFjayc6ICflj43ppognLA0KICAgICAgICAnc3VnZ2VzdGlvbic6ICflu7rorq4nLA0KICAgICAgICAncmVhc29uJzogJ+WOn+WboCcsDQogICAgICAgICdwdXJwb3NlJzogJ+ebrueahCcsDQogICAgICAgICdnb2FsJzogJ+ebruaghycsDQogICAgICAgICdwbGFuJzogJ+iuoeWIkicsDQogICAgICAgICdidWRnZXQnOiAn6aKE566XJywNCiAgICAgICAgJ3JlcXVpcmVtZW50JzogJ+mcgOaxgicsDQogICAgICAgICdleHBlY3RhdGlvbic6ICfmnJ/mnJsnDQogICAgICB9Ow0KDQogICAgICByZXR1cm4gbGFiZWxNYXBbZmllbGROYW1lXSB8fCBudWxsOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5a2X5q6157G75Z6L5qCH562+ICovDQogICAgZ2V0RmllbGRUeXBlTGFiZWwoZmllbGRUeXBlKSB7DQogICAgICBjb25zdCB0eXBlTGFiZWxzID0gew0KICAgICAgICAnaW5wdXQnOiAn5paH5pysJywNCiAgICAgICAgJ3RleHRhcmVhJzogJ+WkmuihjOaWh+acrCcsDQogICAgICAgICdyYWRpbyc6ICfljZXpgIknLA0KICAgICAgICAncmFkaW9fb3RoZXInOiAn5Y2V6YCJK+WFtuS7licsDQogICAgICAgICdjaGVja2JveCc6ICflpJrpgIknLA0KICAgICAgICAnY2hlY2tib3hfb3RoZXInOiAn5aSa6YCJK+WFtuS7licsDQogICAgICAgICdzZWxlY3QnOiAn5LiL5ouJ6YCJ5oupJywNCiAgICAgICAgJ3NlbGVjdF9vdGhlcic6ICfkuIvmi4kr5YW25LuWJywNCiAgICAgICAgJ3BpY2tlcic6ICfmu5rliqjpgInmi6knLA0KICAgICAgICAnZGF0ZSc6ICfml6XmnJ8nLA0KICAgICAgICAnZGF0ZXRpbWUnOiAn5pel5pyf5pe26Ze0JywNCiAgICAgICAgJ3RpbWUnOiAn5pe26Ze0JywNCiAgICAgICAgJ251bWJlcic6ICfmlbDlrZcnLA0KICAgICAgICAnZmlsZSc6ICfmlofku7YnLA0KICAgICAgICAnaW1hZ2UnOiAn5Zu+54mHJw0KICAgICAgfTsNCiAgICAgIHJldHVybiB0eXBlTGFiZWxzW2ZpZWxkVHlwZV0gfHwgZmllbGRUeXBlOw0KICAgIH0sDQoNCiAgICAvKiog6I635Y+W5a2X5q6157G75Z6L5qCH562+6aKc6ImyICovDQogICAgZ2V0RmllbGRUeXBlVGFnVHlwZShmaWVsZFR5cGUpIHsNCiAgICAgIGNvbnN0IHR5cGVDb2xvcnMgPSB7DQogICAgICAgICdpbnB1dCc6ICcnLA0KICAgICAgICAndGV4dGFyZWEnOiAnaW5mbycsDQogICAgICAgICdyYWRpbyc6ICdzdWNjZXNzJywNCiAgICAgICAgJ3JhZGlvX290aGVyJzogJ3N1Y2Nlc3MnLA0KICAgICAgICAnY2hlY2tib3gnOiAnd2FybmluZycsDQogICAgICAgICdjaGVja2JveF9vdGhlcic6ICd3YXJuaW5nJywNCiAgICAgICAgJ3NlbGVjdCc6ICdwcmltYXJ5JywNCiAgICAgICAgJ3NlbGVjdF9vdGhlcic6ICdwcmltYXJ5JywNCiAgICAgICAgJ3BpY2tlcic6ICdwcmltYXJ5JywNCiAgICAgICAgJ2RhdGUnOiAnaW5mbycsDQogICAgICAgICdkYXRldGltZSc6ICdpbmZvJywNCiAgICAgICAgJ3RpbWUnOiAnaW5mbycsDQogICAgICAgICdudW1iZXInOiAnc3VjY2VzcycsDQogICAgICAgICdmaWxlJzogJ2RhbmdlcicsDQogICAgICAgICdpbWFnZSc6ICdkYW5nZXInDQogICAgICB9Ow0KICAgICAgcmV0dXJuIHR5cGVDb2xvcnNbZmllbGRUeXBlXSB8fCAnJzsNCiAgICB9DQogIH0NCn07DQo="}, null]}